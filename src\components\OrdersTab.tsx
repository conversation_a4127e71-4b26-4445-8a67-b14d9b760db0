import React from 'react';
import { orders } from '../data/mockData';

export const OrdersTab: React.FC = () => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'working':
        return 'text-accent';
      case 'filled':
        return 'text-bull';
      case 'canceled':
        return 'text-trading-text-secondary';
      case 'rejected':
        return 'text-bear';
      default:
        return 'text-trading-text';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working':
        return '⚡';
      case 'filled':
        return '✓';
      case 'canceled':
        return '×';
      case 'rejected':
        return '!';
      default:
        return '•';
    }
  };

  return (
    <div className=\"h-full overflow-y-auto p-4\">
      <table className=\"w-full text-xs\">
        <thead>
          <tr className=\"text-trading-text-secondary border-b border-trading-border/50\">
            <th className=\"px-2 py-2 text-left font-medium\">Status</th>
            <th className=\"px-2 py-2 text-left font-medium\">Symbol</th>
            <th className=\"px-2 py-2 text-left font-medium\">Side</th>
            <th className=\"px-2 py-2 text-left font-medium\">Quantity</th>
            <th className=\"px-2 py-2 text-left font-medium\">Price</th>
            <th className=\"px-2 py-2 text-left font-medium\">Type</th>
            <th className=\"px-2 py-2 text-left font-medium\">Stop loss</th>
            <th className=\"px-2 py-2 text-left font-medium\">Take profit</th>
            <th className=\"px-2 py-2 text-left font-medium\">Fill Price</th>
            <th className=\"px-2 py-2 text-left font-medium\">Bid</th>
            <th className=\"px-2 py-2 text-left font-medium\">Ask</th>
            <th className=\"px-2 py-2 text-left font-medium\">Order ID</th>
            <th className=\"px-2 py-2 text-left font-medium\">Time and date</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order) => (
            <tr key={order.id} className=\"hover:bg-trading-border/20 border-b border-trading-border/30\">
              <td className=\"px-2 py-2\">
                <div className=\"flex items-center space-x-2\">
                  <span className={getStatusColor(order.status)}>
                    {getStatusIcon(order.status)}
                  </span>
                  <span className={`${getStatusColor(order.status)} font-medium uppercase`}>
                    {order.status}
                  </span>
                </div>
              </td>
              <td className=\"px-2 py-2 text-trading-text font-medium\">{order.symbol}</td>
              <td className={`px-2 py-2 font-medium ${
                order.side === 'buy' ? 'text-bull' : 'text-bear'
              }`}>
                {order.side.toUpperCase()}
              </td>
              <td className=\"px-2 py-2 text-trading-text\">{order.quantity}</td>
              <td className=\"px-2 py-2 text-trading-text\">{order.price.toFixed(5)}</td>
              <td className=\"px-2 py-2 text-trading-text\">{order.type}</td>
              <td className=\"px-2 py-2 text-trading-text\">
                {order.stopLoss ? order.stopLoss.toFixed(5) : '—'}
              </td>
              <td className=\"px-2 py-2 text-trading-text\">
                {order.takeProfit ? order.takeProfit.toFixed(5) : '—'}
              </td>
              <td className=\"px-2 py-2 text-trading-text\">
                {order.fillPrice ? order.fillPrice.toFixed(5) : '—'}
              </td>
              <td className=\"px-2 py-2 text-trading-text\">{order.bid.toFixed(5)}</td>
              <td className=\"px-2 py-2 text-trading-text\">{order.ask.toFixed(5)}</td>
              <td className=\"px-2 py-2 text-trading-text-secondary font-mono\">{order.id}</td>
              <td className=\"px-2 py-2 text-trading-text-secondary\">{order.timestamp}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};