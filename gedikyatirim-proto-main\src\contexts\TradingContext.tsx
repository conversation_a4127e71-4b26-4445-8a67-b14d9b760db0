import React, { createContext, useContext, useState, ReactNode } from 'react';
import { CurrencyPair, Timeframe } from '../types/trading';
import { currencyPairs } from '../data/mockData';

interface TradingContextType {
  selectedSymbol: string;
  selectedTimeframe: Timeframe;
  setSelectedSymbol: (symbol: string) => void;
  setSelectedTimeframe: (timeframe: Timeframe) => void;
  getCurrentPair: () => CurrencyPair | undefined;
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

export const useTradingContext = () => {
  const context = useContext(TradingContext);
  if (!context) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};

interface TradingProviderProps {
  children: ReactNode;
}

export const TradingProvider: React.FC<TradingProviderProps> = ({ children }) => {
  const [selectedSymbol, setSelectedSymbol] = useState<string>('EUR/CHF');
  const [selectedTimeframe, setSelectedTimeframe] = useState<Timeframe>('15m');

  const getCurrentPair = () => {
    return currencyPairs.find(pair => pair.symbol === selectedSymbol);
  };

  return (
    <TradingContext.Provider
      value={{
        selectedSymbol,
        selectedTimeframe,
        setSelectedSymbol,
        setSelectedTimeframe,
        getCurrentPair
      }}
    >
      {children}
    </TradingContext.Provider>
  );
};