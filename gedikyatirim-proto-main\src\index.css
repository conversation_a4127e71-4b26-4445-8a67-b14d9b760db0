@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Trading Terminal Design System */
@layer base {
  :root {
    /* Base colors - Light Theme */
    --background: 210 17% 95%;
    --foreground: 210 10% 15%;
    
    /* Trading specific colors - Light Theme */
    --trading-bg: 0 0% 98%;
    --trading-panel: 0 0% 100%;
    --trading-border: 210 16% 88%;
    --trading-text: 210 10% 25%;
    --trading-text-secondary: 210 8% 45%;
    
    /* Market colors (same for both themes) */
    --bull-green: 164 76% 39%;
    --bull-green-bg: 164 76% 39% / 0.1;
    --bear-red: 0 84% 60%;
    --bear-red-bg: 0 84% 60% / 0.1;
    
    /* Chart colors - Light Theme */
    --chart-grid: 210 16% 93%;
    --chart-axis: 210 10% 71%;
    --chart-up: 164 76% 39%;
    --chart-down: 0 84% 60%;
    --chart-volume: 210 10% 71% / 0.5;
    
    /* Indicators (same for both themes) */
    --bollinger-upper: 25 95% 53%;
    --bollinger-mid: 217 91% 60%;
    --bollinger-lower: 316 74% 68%;
    
    /* UI Elements - Light Theme */
    --button-buy: 164 76% 39%;
    --button-buy-hover: 164 76% 34%;
    --button-sell: 0 84% 60%;
    --button-sell-hover: 0 84% 55%;
    
    /* Status colors (same for both themes) */
    --status-filled: 164 76% 39%;
    --status-pending: 43 96% 56%;
    --status-canceled: 210 8% 45%;
    --status-rejected: 0 84% 60%;
    --status-info: 217 91% 60%;
    
    /* Legacy shadcn colors */
    --card: 0 0% 100%;
    --card-foreground: 210 10% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 10% 15%;
    --primary: 210 10% 15%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 17% 95%;
    --secondary-foreground: 210 10% 15%;
    --muted: 210 17% 95%;
    --muted-foreground: 210 8% 45%;
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 16% 88%;
    --input: 210 16% 88%;
    --ring: 217 91% 60%;
    --radius: 0.375rem;
  }

  .dark {
    /* Base colors - Dark Theme */
    --background: 220 13% 9%;
    --foreground: 210 40% 95%;
    
    /* Trading specific colors - Dark Theme */
    --trading-bg: 220 13% 9%;
    --trading-panel: 220 13% 12%;
    --trading-border: 220 13% 18%;
    --trading-text: 210 40% 95%;
    --trading-text-secondary: 210 15% 65%;
    
    /* Market colors (enhanced for dark theme) */
    --bull-green: 164 76% 45%;
    --bull-green-bg: 164 76% 45% / 0.15;
    --bear-red: 0 84% 65%;
    --bear-red-bg: 0 84% 65% / 0.15;
    
    /* Chart colors - Dark Theme */
    --chart-grid: 220 13% 15%;
    --chart-axis: 210 15% 55%;
    --chart-up: 164 76% 45%;
    --chart-down: 0 84% 65%;
    --chart-volume: 210 15% 55% / 0.4;
    
    /* UI Elements - Dark Theme */
    --button-buy: 164 76% 45%;
    --button-buy-hover: 164 76% 50%;
    --button-sell: 0 84% 65%;
    --button-sell-hover: 0 84% 70%;
    
    /* Status colors (enhanced for dark theme) */
    --status-filled: 164 76% 45%;
    --status-pending: 43 96% 60%;
    --status-canceled: 210 15% 55%;
    --status-rejected: 0 84% 65%;
    --status-info: 217 91% 65%;
    
    /* Legacy shadcn dark colors */
    --card: 220 13% 12%;
    --card-foreground: 210 40% 95%;
    --popover: 220 13% 12%;
    --popover-foreground: 210 40% 95%;
    --primary: 210 40% 95%;
    --primary-foreground: 220 13% 9%;
    --secondary: 220 13% 15%;
    --secondary-foreground: 210 40% 95%;
    --muted: 220 13% 15%;
    --muted-foreground: 210 15% 65%;
    --accent: 217 91% 65%;
    --accent-foreground: 220 13% 9%;
    --destructive: 0 84% 65%;
    --destructive-foreground: 210 40% 95%;
    --border: 220 13% 18%;
    --input: 220 13% 18%;
    --ring: 217 91% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-trading-bg text-trading-text font-inter antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }
  
  /* Trading terminal specific styles */
  .panel-border {
    @apply border border-trading-border;
  }
  
  .panel-bg {
    @apply bg-trading-panel;
  }
  
  .text-bull {
    @apply text-bull-green;
  }
  
  .text-bear {
    @apply text-bear-red;
  }
  
  .bg-bull {
    @apply bg-bull-green;
  }
  
  .bg-bear {
    @apply bg-bear-red;
  }
  
  .bg-bull-subtle {
    @apply bg-bull-green-bg;
  }
  
  .bg-bear-subtle {
    @apply bg-bear-red-bg;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}