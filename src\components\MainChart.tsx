import React, { useEffect, useRef, useState } from 'react';
import { init, dispose } from 'klinecharts';
import type { Chart } from 'klinecharts';
import { useKLineData } from '../hooks/useKLineData';
import { useTradingContext } from '../contexts/TradingContext';
import { ChevronDown, BarChart3, TrendingUp, Settings, Plus } from 'lucide-react';

export const MainChart: React.FC = () => {
  const { selectedSymbol, selectedTimeframe, setSelectedTimeframe, getCurrentPair } = useTradingContext();
  const currentPair = getCurrentPair();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  const [showIndicators, setShowIndicators] = useState(false);
  
  // Get mock candlestick data
  const klineData = useKLineData(selectedSymbol, selectedTimeframe);

  const timeframes = [
    { id: '1m', label: '1m' },
    { id: '5m', label: '5m' },
    { id: '15m', label: '15m' },
    { id: '30m', label: '30m' },
    { id: '1h', label: '1h' },
    { id: '4h', label: '4h' },
    { id: '1d', label: '1d' },
  ];

  const indicators = [
    { id: 'MA', label: 'Moving Average', enabled: true },
    { id: 'BOLL', label: 'Bollinger Bands', enabled: true },
    { id: 'RSI', label: 'RSI', enabled: true },
    { id: 'MACD', label: 'MACD', enabled: false },
    { id: 'VOL', label: 'Volume', enabled: true },
  ];

  const toggleIndicator = (indicatorId: string) => {
    if (chartInstanceRef.current) {
      const chart = chartInstanceRef.current;
      const currentIndicators = chart.getIndicators() || [];
      const hasIndicator = currentIndicators.some(ind => ind.name === indicatorId);
      
      if (hasIndicator) {
        chart.removeIndicator(indicatorId);
      } else {
        if (indicatorId === 'VOL') {
          chart.createIndicator(indicatorId, true, { id: 'candle_pane' });
        } else if (indicatorId === 'MA' || indicatorId === 'BOLL') {
          chart.createIndicator(indicatorId, false, { id: 'candle_pane' });
        } else {
          chart.createIndicator(indicatorId, true);
        }
      }
    }
  };

  // Initialize chart
  useEffect(() => {
    if (!chartRef.current) return;

    // Create chart instance
    const chart = init(chartRef.current);
    chartInstanceRef.current = chart;

    // Configure chart styles for professional trading look
    chart.setStyles({
      grid: {
        show: true,
        horizontal: {
          show: true,
          size: 1,
          color: '#E5E7EB20',
          style: 'solid'
        },
        vertical: {
          show: true,
          size: 1,
          color: '#E5E7EB20',
          style: 'solid'
        }
      },
      candle: {
        type: 'candle_solid',
        bar: {
          upColor: '#22C55E',
          downColor: '#EF4444',
          noChangeColor: '#9CA3AF',
        },
        tooltip: {
          showRule: 'always',
          showType: 'standard',
          labels: ['T:', 'O:', 'H:', 'L:', 'C:', 'V:']
        }
      },
      xAxis: {
        show: true,
        height: 50,
        axisLine: {
          show: true,
          color: '#E5E7EB',
          size: 1
        },
        tickText: {
          show: true,
          color: '#6B7280',
          size: 12
        },
        tickLine: {
          show: true,
          size: 1,
          length: 3,
          color: '#E5E7EB'
        }
      },
      yAxis: {
        show: true,
        width: 80,
        position: 'right',
        type: 'normal',
        inside: false,
        reverse: false,
        axisLine: {
          show: true,
          color: '#E5E7EB',
          size: 1
        },
        tickText: {
          show: true,
          color: '#6B7280',
          size: 12
        },
        tickLine: {
          show: true,
          size: 1,
          length: 3,
          color: '#E5E7EB'
        }
      },
      crosshair: {
        show: true,
        horizontal: {
          show: true,
          line: {
            show: true,
            style: 'dash',
            dashValue: [4, 2],
            size: 1,
            color: '#3B82F6'
          },
          text: {
            show: true,
            color: '#FFFFFF',
            backgroundColor: '#3B82F6',
            size: 12,
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 2,
            paddingBottom: 2,
            borderRadius: 2
          }
        },
        vertical: {
          show: true,
          line: {
            show: true,
            style: 'dash',
            dashValue: [4, 2],
            size: 1,
            color: '#3B82F6'
          },
          text: {
            show: true,
            color: '#FFFFFF',
            backgroundColor: '#3B82F6',
            size: 12,
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 2,
            paddingBottom: 2,
            borderRadius: 2
          }
        }
      }
    });

    // Create volume indicator
    chart.createIndicator('VOL', true, { id: 'candle_pane' });
    
    // Create technical indicators
    chart.createIndicator('MA', false, { id: 'candle_pane' });
    chart.createIndicator('BOLL', false, { id: 'candle_pane' }); // Bollinger Bands
    chart.createIndicator('RSI', true);

    // Cleanup function
    return () => {
      if (chartInstanceRef.current) {
        dispose(chartRef.current!);
        chartInstanceRef.current = null;
      }
    };
  }, []);

  // Update chart data when symbol or timeframe changes
  useEffect(() => {
    if (chartInstanceRef.current && klineData.length > 0) {
      chartInstanceRef.current.applyNewData(klineData, true);
    }
  }, [klineData]);

  // Handle chart resize
  useEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.indicators-dropdown')) {
        setShowIndicators(false);
      }
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="h-full bg-white dark:bg-gray-900 flex flex-col">
      {/* Chart Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="font-medium text-gray-900 dark:text-white">{selectedSymbol}</span>
            <span className="text-sm text-gray-600 dark:text-gray-400">{currentPair?.name}</span>
            <ChevronDown className="h-4 w-4 text-gray-600 dark:text-gray-400" />
          </div>
          
          {currentPair && (
            <div className="flex items-center space-x-4 text-sm">
              <div className="text-gray-600 dark:text-gray-400">
                Bid: <span className="text-red-600 font-medium">{currentPair.bid.toFixed(5)}</span>
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                Ask: <span className="text-green-600 font-medium">{currentPair.ask.toFixed(5)}</span>
              </div>
              <div className={`font-medium ${currentPair.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {currentPair.changePercent >= 0 ? '+' : ''}{currentPair.changePercent.toFixed(2)}%
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {/* Timeframe Buttons */}
          <div className="flex items-center space-x-1">
            {timeframes.map((tf) => (
              <button
                key={tf.id}
                onClick={() => setSelectedTimeframe(tf.id as any)}
                className={`px-2 py-1 text-xs font-medium rounded transition-colors ${
                  selectedTimeframe === tf.id
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                {tf.label}
              </button>
            ))}
          </div>

          {/* Chart Tools */}
          <div className="flex items-center space-x-2">
            <button className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              <BarChart3 className="h-4 w-4" />
            </button>
            
            {/* Indicators Dropdown */}
            <div className="relative indicators-dropdown">
              <button 
                onClick={() => setShowIndicators(!showIndicators)}
                className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
              >
                <TrendingUp className="h-4 w-4" />
              </button>
              
              {showIndicators && (
                <div className="absolute right-0 top-8 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                  <div className="p-2">
                    <div className="text-sm font-medium text-gray-900 dark:text-white mb-2">Technical Indicators</div>
                    {indicators.map((indicator) => (
                      <button
                        key={indicator.id}
                        onClick={() => toggleIndicator(indicator.id)}
                        className="w-full text-left px-2 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded flex items-center justify-between"
                      >
                        <span>{indicator.label}</span>
                        <div className={`w-2 h-2 rounded-full ${
                          indicator.enabled ? 'bg-green-500' : 'bg-gray-400'
                        }`} />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <button className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className="flex-1 relative">
        <div 
          ref={chartRef} 
          className="absolute inset-0 w-full h-full"
          style={{ minHeight: '400px' }}
        />
        
        {/* Current Price Indicator */}
        {currentPair && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2 z-10">
            <div className={`px-3 py-1.5 rounded-l text-xs font-bold text-white shadow-lg ${
              currentPair.changePercent >= 0 ? 'bg-green-600' : 'bg-red-600'
            }`}>
              <div className="text-center">
                <div className="leading-tight">{currentPair.ask.toFixed(currentPair.symbol.includes('JPY') ? 3 : 5)}</div>
                <div className="text-xs opacity-90">
                  {currentPair.changePercent >= 0 ? '↑' : '↓'} {Math.abs(currentPair.changePercent).toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};