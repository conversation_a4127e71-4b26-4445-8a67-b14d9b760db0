import React, { useState, useEffect } from 'react';
import { useTradingContext } from '../contexts/TradingContext';

const PairRow: React.FC<{ pair: any, isSelected: boolean, onClick: () => void }> = ({ pair, isSelected, onClick }) => {
  const [priceFlash, setPriceFlash] = useState<'up' | 'down' | null>(null);
  const [prevBid, setPrevBid] = useState(pair.bid);

  useEffect(() => {
    if (pair.bid !== prevBid) {
      setPriceFlash(pair.bid > prevBid ? 'up' : 'down');
      setPrevBid(pair.bid);
      
      const timer = setTimeout(() => setPriceFlash(null), 300);
      return () => clearTimeout(timer);
    }
  }, [pair.bid, prevBid]);

  return (
    <tr 
      className={`cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800 ${
        isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border-l-2 border-blue-500' : ''
      } ${
        priceFlash === 'up' ? 'bg-green-50 dark:bg-green-900/10' : 
        priceFlash === 'down' ? 'bg-red-50 dark:bg-red-900/10' : ''
      }`}
      onClick={onClick}
    >
      <td className="px-2 py-1.5 text-xs font-medium text-gray-900 dark:text-white">
        {pair.symbol}
      </td>
      <td className={`px-2 py-1.5 text-xs transition-colors duration-200 ${
        priceFlash === 'down' ? 'text-red-600 font-semibold' : 'text-gray-700 dark:text-gray-300'
      }`}>
        {pair.bid.toFixed(pair.symbol.includes('JPY') ? 3 : 5)}
      </td>
      <td className={`px-2 py-1.5 text-xs transition-colors duration-200 ${
        priceFlash === 'up' ? 'text-green-600 font-semibold' : 'text-gray-700 dark:text-gray-300'
      }`}>
        {pair.ask.toFixed(pair.symbol.includes('JPY') ? 3 : 5)}
      </td>
      <td className={`px-2 py-1.5 text-xs font-medium flex items-center ${
        pair.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
      }`}>
        <span className="mr-1">
          {pair.changePercent >= 0 ? '↗' : '↘'}
        </span>
        {Math.abs(pair.changePercent).toFixed(2)}%
      </td>
    </tr>
  );
};

export const Watchlist: React.FC = () => {
  const { selectedSymbol, setSelectedSymbol, currencyPairs } = useTradingContext();
  
  const eurPairs = currencyPairs.filter(pair => pair.symbol.startsWith('EUR'));
  const usdPairs = currencyPairs.filter(pair => pair.symbol.includes('USD') && !pair.symbol.startsWith('EUR'));

  return (
    <div className="flex-1 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden">
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Watchlist</h3>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {/* EUR Pairs Section */}
        <div className="p-2">
          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">EUR</div>
          <table className="w-full text-xs">
            <thead>
              <tr className="text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                <th className="px-2 py-1 text-left font-medium">Symbol</th>
                <th className="px-2 py-1 text-left font-medium">Bid</th>
                <th className="px-2 py-1 text-left font-medium">Ask</th>
                <th className="px-2 py-1 text-left font-medium">%</th>
              </tr>
            </thead>
            <tbody>
              {eurPairs.map((pair) => (
                <PairRow
                  key={pair.symbol}
                  pair={pair}
                  isSelected={selectedSymbol === pair.symbol}
                  onClick={() => setSelectedSymbol(pair.symbol)}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* USD Pairs Section */}
        <div className="p-2">
          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">USD</div>
          <table className="w-full text-xs">
            <thead>
              <tr className="text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                <th className="px-2 py-1 text-left font-medium">Symbol</th>
                <th className="px-2 py-1 text-left font-medium">Bid</th>
                <th className="px-2 py-1 text-left font-medium">Ask</th>
                <th className="px-2 py-1 text-left font-medium">%</th>
              </tr>
            </thead>
            <tbody>
              {usdPairs.map((pair) => (
                <PairRow
                  key={pair.symbol}
                  pair={pair}
                  isSelected={selectedSymbol === pair.symbol}
                  onClick={() => setSelectedSymbol(pair.symbol)}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};