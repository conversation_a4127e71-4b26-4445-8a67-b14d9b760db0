export interface CurrencyPair {
  symbol: string;
  name: string;
  bid: number;
  ask: number;
  change: number;
  changePercent: number;
}

export interface ChartDataPoint {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface Order {
  id: string;
  status: 'pending' | 'filled' | 'canceled' | 'rejected' | 'working' | 'expired';
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  type: string;
  stopLoss?: number;
  takeProfit?: number;
  fillPrice?: number;
  bid: number;
  ask: number;
  timestamp: string;
}

export interface Position {
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  profitLoss: number;
  profitLossPercent: number;
}

export interface Message {
  id: string;
  type: 'filled' | 'canceled' | 'rejected' | 'info' | 'pending';
  content: string;
  timestamp: string;
  orderId?: string;
}

export interface AccountSummary {
  balance: number;
  equity: number;
  fpl: number;
  usedMargin: number;
  usableMargin: number;
  account: string;
  currency: string;
  user: string;
}

export type Timeframe = '1m' | '5m' | '15m' | '1H' | '4H' | '1D';