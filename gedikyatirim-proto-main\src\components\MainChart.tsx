import React from 'react';
import { ChevronDown, BarChart3, TrendingUp, Settings } from 'lucide-react';
import { useTradingContext } from '../contexts/TradingContext';
import { Timeframe } from '../types/trading';

export const MainChart: React.FC = () => {
  const { selectedSymbol, selectedTimeframe, setSelectedTimeframe, getCurrentPair } = useTradingContext();
  const currentPair = getCurrentPair();

  const timeframes: Timeframe[] = ['1m', '5m', '15m', '1H', '4H', '1D'];

  return (
    <div className="h-full panel-bg panel-border flex flex-col">
      {/* Chart Header */}
      <div className="p-3 border-b border-trading-border flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="font-medium text-trading-text">{selectedSymbol}</span>
            <span className="text-sm text-trading-text-secondary">{currentPair?.name}</span>
            <ChevronDown className="h-4 w-4 text-trading-text-secondary" />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Timeframe Buttons */}
          <div className="flex items-center space-x-1">
            {timeframes.map((tf) => (
              <button
                key={tf}
                onClick={() => setSelectedTimeframe(tf)}
                className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                  selectedTimeframe === tf
                    ? 'bg-accent text-accent-foreground'
                    : 'text-trading-text-secondary hover:text-trading-text hover:bg-trading-border/50'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>

          {/* Chart Tools */}
          <div className="flex items-center space-x-2">
            <button className="p-1.5 text-trading-text-secondary hover:text-trading-text">
              <BarChart3 className="h-4 w-4" />
            </button>
            <button className="p-1.5 text-trading-text-secondary hover:text-trading-text">
              <TrendingUp className="h-4 w-4" />
            </button>
            <button className="p-1.5 text-trading-text-secondary hover:text-trading-text">
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Chart Area */}
      <div className="flex-1 relative bg-gradient-to-b from-trading-panel to-chart-grid/10">
        {/* Mock Chart with Candlesticks and Volume */}
        <svg width="100%" height="100%" className="absolute inset-0">
          {/* Grid Lines */}
          <defs>
            <pattern id="grid" width="50" height="30" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 30" fill="none" stroke="hsl(var(--chart-grid))" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Bollinger Bands */}
          <path 
            d="M0,120 Q150,110 300,130 Q450,125 600,135 Q750,140 900,130" 
            stroke="hsl(var(--bollinger-upper))" 
            strokeWidth="1.5" 
            fill="none"
            opacity="0.7"
          />
          <path 
            d="M0,160 Q150,150 300,170 Q450,165 600,175 Q750,180 900,170" 
            stroke="hsl(var(--bollinger-mid))" 
            strokeWidth="1.5" 
            fill="none"
            opacity="0.8"
          />
          <path 
            d="M0,200 Q150,190 300,210 Q450,205 600,215 Q750,220 900,210" 
            stroke="hsl(var(--bollinger-lower))" 
            strokeWidth="1.5" 
            fill="none"
            opacity="0.7"
          />

          {/* Mock Candlesticks */}
          {Array.from({ length: 20 }, (_, i) => {
            const x = 50 + i * 40;
            const isGreen = Math.random() > 0.5;
            const high = 140 + Math.random() * 40;
            const low = high - 20 - Math.random() * 20;
            const open = low + Math.random() * (high - low);
            const close = low + Math.random() * (high - low);
            const bodyTop = Math.min(open, close);
            const bodyBottom = Math.max(open, close);

            return (
              <g key={i}>
                {/* Wick */}
                <line
                  x1={x}
                  y1={high}
                  x2={x}
                  y2={low}
                  stroke={isGreen ? 'hsl(var(--chart-up))' : 'hsl(var(--chart-down))'}
                  strokeWidth="1"
                />
                {/* Body */}
                <rect
                  x={x - 4}
                  y={bodyTop}
                  width="8"
                  height={Math.max(1, bodyBottom - bodyTop)}
                  fill={isGreen ? 'hsl(var(--chart-up))' : 'hsl(var(--chart-down))'}
                />
              </g>
            );
          })}

          {/* Volume bars at bottom */}
          {Array.from({ length: 20 }, (_, i) => {
            const x = 50 + i * 40;
            const height = 10 + Math.random() * 30;
            const y = 320 - height;

            return (
              <rect
                key={`vol-${i}`}
                x={x - 3}
                y={y}
                width="6"
                height={height}
                fill="hsl(var(--chart-volume))"
              />
            );
          })}

          {/* Price Scale */}
          {[140, 160, 180, 200, 220].map((price, i) => (
            <g key={price}>
              <line
                x1="0"
                y1={120 + i * 20}
                x2="100%"
                y2={120 + i * 20}
                stroke="hsl(var(--chart-grid))"
                strokeWidth="0.5"
                strokeDasharray="2,2"
              />
              <text
                x="95%"
                y={120 + i * 20 - 3}
                className="text-xs fill-chart-axis"
                textAnchor="end"
              >
                {(currentPair?.bid || 1.0 + (220 - price) * 0.001).toFixed(5)}
              </text>
            </g>
          ))}
        </svg>

        {/* Current Price Indicator */}
        <div className="absolute right-4 top-1/2 -translate-y-1/2 bg-accent text-accent-foreground px-2 py-1 rounded text-xs font-medium">
          {currentPair?.bid.toFixed(5)}
        </div>
      </div>
    </div>
  );
};