import React, { useState } from 'react';

const OrdersTab: React.FC = () => (
  <div className="h-full overflow-y-auto p-4">
    <div className="text-center text-gray-500 dark:text-gray-400">
      <div className="text-lg mb-2">📊</div>
      <div>No orders</div>
      <div className="text-xs mt-1">Your trading orders will appear here</div>
    </div>
  </div>
);

const PositionsTab: React.FC = () => (
  <div className="h-full overflow-y-auto p-4">
    <div className="text-center text-gray-500 dark:text-gray-400">
      <div className="text-lg mb-2">📈</div>
      <div>No positions</div>
      <div className="text-xs mt-1">Your open positions will appear here</div>
    </div>
  </div>
);

const AlertsTab: React.FC = () => (
  <div className="h-full overflow-y-auto p-4">
    <div className="text-center text-gray-500 dark:text-gray-400">
      <div className="text-lg mb-2">🔔</div>
      <div>No alerts</div>
      <div className="text-xs mt-1">Set up price alerts to monitor your symbols</div>
    </div>
  </div>
);

const ChartTab: React.FC = () => (
  <div className="h-full overflow-y-auto p-4">
    <div className="text-center text-gray-500 dark:text-gray-400">
      <div className="text-lg mb-2">📊</div>
      <div>Chart analysis</div>
      <div className="text-xs mt-1">Additional chart tools and analysis</div>
    </div>
  </div>
);

export const BottomPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('orders');

  const tabs = [
    { id: 'orders', label: 'Orders', component: OrdersTab },
    { id: 'positions', label: 'Positions', component: PositionsTab },
    { id: 'alerts', label: 'Alerts', component: AlertsTab },
    { id: 'chart', label: 'Chart', component: ChartTab }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || OrdersTab;

  return (
    <div className="h-48 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Tab Navigation */}
      <div className="flex items-center border-b border-gray-200 dark:border-gray-700 px-4">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {tab.label}
          </button>
        ))}
        
        {/* Filter Controls */}
        <div className="ml-auto flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
          <span>Filtered by</span>
          <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">All</span>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        <ActiveComponent />
      </div>
    </div>
  );
};