import React, { useState, useEffect } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import { Wifi, WifiOff, Loader2, BarChart3 } from 'lucide-react';
import { formatPrice } from '../utils/priceFormatting';
import TradingChart from './TradingChart';

interface PriceQuotesPanelProps {
  onSymbolSelect?: (symbol: string) => void;
}

const PriceQuotesPanel: React.FC<PriceQuotesPanelProps> = ({ onSymbolSelect }) => {
  const { 
    connectionStatus, 
    quotes
  } = useWebSocket();
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [contextMenu, setContextMenu] = useState<{ symbol: string; x: number; y: number } | null>(null);
  const [selectedChartSymbol, setSelectedChartSymbol] = useState<string | null>(null);

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const handleClickOutside = () => setContextMenu(null);
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setContextMenu(null);
      }
    };
    
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleRowClick = (symbol: string) => {
    if (onSymbolSelect) {
      onSymbolSelect(symbol);
    }
  };

  const handleRightClick = (e: React.MouseEvent, symbol: string) => {
    e.preventDefault();
    setContextMenu({
      symbol,
      x: e.clientX,
      y: e.clientY
    });
  };

  const handleShowChart = (symbol: string) => {
    setSelectedChartSymbol(symbol);
    setContextMenu(null);
  };

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-400" />;
      case 'connecting':
        return <Loader2 className="w-4 h-4 text-orange-400 animate-spin" />;
      default:
        return <WifiOff className="w-4 h-4 text-red-400" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      default:
        return 'Connection Lost';
    }
  };

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-400';
      case 'connecting':
        return 'text-orange-400';
      default:
        return 'text-red-400';
    }
  };

  const formatTime = (timeString: string, symbol: string) => {
    // For demo purposes - show AUDUSD as stale data
    if (symbol === 'AUDUSD') {
      return '-';
    }

    // Handle the time string format from WebSocket (e.g., "10:32:17 AM")
    try {
      // Try to parse as a time string by creating a date for today with that time
      const today = new Date().toDateString();
      const fullDateTime = new Date(`${today} ${timeString}`);
      
      // Check if the date is valid
      if (isNaN(fullDateTime.getTime())) {
        // If parsing fails, just return "-"
        return "-";
      }
      
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - fullDateTime.getTime()) / 1000);
      const diffInDays = diffInSeconds / (24 * 60 * 60);
      
      // If more than 1 day old, show "-"
      if (Math.abs(diffInDays) > 1) {
        return "-";
      }
      
      // Otherwise show time in hh:mm format
      return fullDateTime.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit', 
        hour12: false 
      });
    } catch (error) {
      // Fallback: return "-" for any parsing errors
      return "-";
    }
  };

  // Responsive column configuration - adjusted for panel width constraints
  const getVisibleColumns = () => {
    // Since this panel is 1/5 of screen width, we need different breakpoints
    // At 1920px screen, panel width is ~384px
    // At 1400px screen, panel width is ~280px  
    // At 1100px screen, panel width is ~220px
    
    if (screenWidth < 1200) {
      return ['symbol', 'bid', 'ask', 'change']; // Hide spread & time on smaller screens
    } else if (screenWidth < 1600) {
      return ['symbol', 'bid', 'ask', 'spread', 'change']; // Hide time on medium screens
    } else {
      return ['symbol', 'bid', 'ask', 'spread', 'change', 'time']; // Show all on large screens
    }
  };

  const visibleColumns = getVisibleColumns();

  return (
    <>
      <div className="bg-slate-800 rounded-lg p-2 sm:p-4 border border-slate-700 w-full h-full flex flex-col">
        {/* Header with Connection Status */}
        <div className="flex items-center justify-between mb-2 sm:mb-4 flex-shrink-0">
          <h2 className="text-base sm:text-lg font-semibold text-white">Price Quotes</h2>
          <div className="flex items-center space-x-2">
            {getConnectionIcon()}
            <span className={`text-xs sm:text-sm ${getConnectionColor()}`}>
              {getConnectionText()}
            </span>
          </div>
        </div>
      
      {quotes.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="text-gray-400 text-sm">Could not load watchlist.</p>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto border border-slate-600 rounded-lg">
          <table className="w-full text-xs">
            <thead className="sticky top-0 bg-slate-800 z-10">
              <tr className="border-b border-slate-600">
                {visibleColumns.includes('symbol') && (
                  <th className="text-left px-1 py-2 text-xs font-medium text-gray-400 w-16">Symbol</th>
                )}
                {visibleColumns.includes('bid') && (
                  <th className="text-right px-1 py-2 text-xs font-medium text-gray-400 w-16">Bid</th>
                )}
                {visibleColumns.includes('ask') && (
                  <th className="text-right px-1 py-2 text-xs font-medium text-gray-400 w-16">Ask</th>
                )}
                {visibleColumns.includes('spread') && (
                  <th className="text-right px-1 py-2 text-xs font-medium text-gray-400 w-12">Spread</th>
                )}
                {visibleColumns.includes('change') && (
                  <th className="text-right px-1 py-2 text-xs font-medium text-gray-400 w-16">Change</th>
                )}
                {visibleColumns.includes('time') && (
                  <th className="text-right px-1 py-2 text-xs font-medium text-gray-400 w-14">Time</th>
                )}
              </tr>
            </thead>
            <tbody>
              {quotes.map((quote) => {
                return (
                  <tr 
                    key={quote.symbol} 
                    className="border-b border-slate-700 hover:bg-slate-700/50 cursor-pointer"
                    onClick={() => handleRowClick(quote.symbol)}
                    onContextMenu={(e) => handleRightClick(e, quote.symbol)}
                  >
                    {visibleColumns.includes('symbol') && (
                      <td className="px-1 py-1.5 font-medium text-xs text-white">
                        <span>{quote.symbol}</span>
                      </td>
                    )}
                    {visibleColumns.includes('bid') && (
                      <td className="px-1 py-1.5 font-mono text-right text-xs whitespace-nowrap text-blue-400">
                        {formatPrice(quote.bid, quote.symbol)}
                      </td>
                    )}
                    {visibleColumns.includes('ask') && (
                      <td className="px-1 py-1.5 font-mono text-right text-xs whitespace-nowrap text-red-400">
                        {formatPrice(quote.ask, quote.symbol)}
                      </td>
                    )}
                    {visibleColumns.includes('spread') && (
                      <td className="px-1 py-1.5 font-mono text-right text-xs text-gray-300">
                        {quote.spread.toFixed(1)}
                      </td>
                    )}
                    {visibleColumns.includes('change') && (
                      <td className="px-1 py-1.5 font-mono text-right text-xs">
                        <span className={
                          quote.dailyChange >= 0 
                            ? 'text-green-400' 
                            : 'text-red-400'
                        }>
                          {`${quote.dailyChange >= 0 ? '+' : ''}${(quote.dailyChange * 100).toFixed(1)}%`}
                        </span>
                      </td>
                    )}
                    {visibleColumns.includes('time') && (
                      <td className="px-1 py-1.5 text-right text-xs text-gray-400">
                        {formatTime(quote.time, quote.symbol)}
                      </td>
                    )}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Right-click Context Menu */}
      {contextMenu && (
        <div
          className="fixed bg-slate-800 border border-slate-600 rounded-lg shadow-lg py-2 z-50"
          style={{
            left: `${contextMenu.x}px`,
            top: `${contextMenu.y}px`,
          }}
        >
          <button
            onClick={() => handleShowChart(contextMenu.symbol)}
            className="w-full px-4 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <BarChart3 className="w-4 h-4 text-blue-400" />
            <span>View Chart - {contextMenu.symbol}</span>
          </button>
        </div>
      )}
    </div>

    {/* Trading Chart Modal */}
    {selectedChartSymbol && (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
        <div className="max-w-6xl w-full max-h-full overflow-auto">
          <TradingChart 
            symbol={selectedChartSymbol}
            onClose={() => setSelectedChartSymbol(null)}
            className="w-full"
          />
        </div>
      </div>
    )}
    </>
  );
};

export default PriceQuotesPanel;
