import React, { useState, useEffect } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Slider } from './ui/slider';
import { Switch } from './ui/switch';
import { Separator } from './ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { formatPrice } from '../utils/priceFormatting';
import { TrendingUp, TrendingDown, Percent, DollarSign, Info } from 'lucide-react';

interface TradePanelProps {
  className?: string;
}

type OrderMode = 'Spot' | 'Cross' | 'Isolated' | 'Grid';
type OrderType = 'Limit' | 'Market' | 'Stop Limit';
type TradeDirection = 'BUY' | 'SELL';

const TradePanel: React.FC<TradePanelProps> = ({ className = '' }) => {
  const { quotes, traderAccount, addOrder, addAuditEntry } = useWebSocket();
  
  // Trading state
  const [orderMode, setOrderMode] = useState<OrderMode>('Spot');
  const [orderType, setOrderType] = useState<OrderType>('Limit');
  const [tradeDirection, setTradeDirection] = useState<TradeDirection>('BUY');
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');
  const [price, setPrice] = useState('');
  const [amount, setAmount] = useState('');
  const [amountCurrency, setAmountCurrency] = useState('BTC');
  const [priceCurrency, setPriceCurrency] = useState('USDT');
  const [percentageAmount, setPercentageAmount] = useState([25]);
  
  // TP/SL state
  const [tpslEnabled, setTpslEnabled] = useState(false);
  const [tpLimit, setTpLimit] = useState('');
  const [tpOffset, setTpOffset] = useState('');
  const [tpOffsetPercent, setTpOffsetPercent] = useState([2]);
  const [slTrigger, setSlTrigger] = useState('');
  const [slOffset, setSlOffset] = useState('');
  const [slOffsetPercent, setSlOffsetPercent] = useState([2]);
  
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get current quote for selected symbol
  const currentQuote = quotes.find(q => q.symbol === selectedSymbol);
  const currentPrice = currentQuote ? (tradeDirection === 'BUY' ? currentQuote.ask : currentQuote.bid) : 0;

  // Auto-fill price for market orders
  useEffect(() => {
    if (orderType === 'Market' && currentQuote) {
      setPrice(currentPrice.toFixed(5));
    }
  }, [orderType, currentQuote, tradeDirection, currentPrice]);

  // Calculate totals
  const calculateTotal = () => {
    const priceNum = parseFloat(price) || 0;
    const amountNum = parseFloat(amount) || 0;
    return priceNum * amountNum;
  };

  const calculateAmountByPercentage = () => {
    if (!traderAccount) return;
    const percentage = percentageAmount[0] / 100;
    const availableBalance = traderAccount.balance - traderAccount.usedMargin;
    const calculatedAmount = (availableBalance * percentage) / (parseFloat(price) || 1);
    setAmount(calculatedAmount.toFixed(6));
  };

  useEffect(() => {
    if (percentageAmount[0] > 0 && price && traderAccount) {
      calculateAmountByPercentage();
    }
  }, [percentageAmount, price, traderAccount]);

  const handleSubmitOrder = async () => {
    setIsLoading(true);
    setErrors({});

    try {
      // Validation
      const newErrors: Record<string, string> = {};
      
      if (!selectedSymbol) newErrors.symbol = 'Symbol is required';
      if (!amount || parseFloat(amount) <= 0) newErrors.amount = 'Valid amount is required';
      if (orderType !== 'Market' && (!price || parseFloat(price) <= 0)) {
        newErrors.price = 'Valid price is required';
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        return;
      }

      // Create order
      const newOrder = {
        orderId: `ORD-${Date.now()}`,
        symbol: selectedSymbol,
        type: tradeDirection,
        volume: parseFloat(amount),
        orderPrice: parseFloat(price),
        sl: slTrigger ? parseFloat(slTrigger) : undefined,
        tp: tpLimit ? parseFloat(tpLimit) : undefined,
        currentPrice: currentPrice.toFixed(5),
        state: 'PENDING',
        placementTime: new Date().toISOString(),
        comment: `${orderMode} ${orderType} order`
      };

      addOrder(newOrder);
      addAuditEntry({
        id: `AUDIT-${Date.now()}`,
        timestamp: new Date().toISOString(),
        action: 'CREATE',
        entityType: 'Order',
        entityId: newOrder.orderId,
        details: `Created ${tradeDirection} ${orderType} order for ${amount} ${selectedSymbol} at ${price}`,
        symbol: selectedSymbol
      });

      // Reset form
      setAmount('');
      setPrice('');
      setPercentageAmount([25]);
      setTpLimit('');
      setTpOffset('');
      setSlTrigger('');
      setSlOffset('');
      
    } catch (error) {
      console.error('Order submission failed:', error);
      setErrors({ submit: 'Failed to submit order' });
    } finally {
      setIsLoading(false);
    }
  };

  const orderModes: OrderMode[] = ['Spot', 'Cross', 'Isolated', 'Grid'];
  const orderTypes: OrderType[] = ['Limit', 'Market', 'Stop Limit'];
  const availableSymbols = quotes.map(q => q.symbol);

  return (
    <Card className={`bg-slate-800 border-slate-700 ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-lg">Trade Panel</CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Order Mode Selector */}
        <div className="flex space-x-1 bg-slate-700 rounded-lg p-1">
          {orderModes.map((mode) => (
            <Button
              key={mode}
              variant={orderMode === mode ? "default" : "ghost"}
              size="sm"
              onClick={() => setOrderMode(mode)}
              className={`flex-1 text-xs ${
                orderMode === mode
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'text-gray-400 hover:text-white hover:bg-slate-600'
              }`}
            >
              {mode}
            </Button>
          ))}
        </div>

        {/* Buy/Sell Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant={tradeDirection === 'BUY' ? "default" : "outline"}
            onClick={() => setTradeDirection('BUY')}
            className={`${
              tradeDirection === 'BUY'
                ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                : 'bg-slate-700 hover:bg-slate-600 text-gray-300 border-slate-600'
            }`}
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            Buy
          </Button>
          <Button
            variant={tradeDirection === 'SELL' ? "default" : "outline"}
            onClick={() => setTradeDirection('SELL')}
            className={`${
              tradeDirection === 'SELL'
                ? 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                : 'bg-slate-700 hover:bg-slate-600 text-gray-300 border-slate-600'
            }`}
          >
            <TrendingDown className="w-4 h-4 mr-2" />
            Sell
          </Button>
        </div>

        {/* Order Type & Symbol */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <Label className="text-gray-300 text-xs">Order Type</Label>
            <Select value={orderType} onValueChange={(value: OrderType) => setOrderType(value)}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {orderTypes.map((type) => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label className="text-gray-300 text-xs">Symbol</Label>
            <Select value={selectedSymbol} onValueChange={setSelectedSymbol}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableSymbols.map((symbol) => (
                  <SelectItem key={symbol} value={symbol}>{symbol}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Price Input */}
        {orderType !== 'Market' && (
          <div>
            <Label className="text-gray-300 text-xs">Price</Label>
            <div className="flex">
              <Input
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                placeholder="114,440.00"
                className="bg-slate-700 border-slate-600 text-white rounded-r-none"
                type="number"
                step="0.00001"
              />
              <div className="bg-slate-600 border border-l-0 border-slate-600 rounded-r px-3 flex items-center">
                <span className="text-gray-300 text-sm">{priceCurrency}</span>
              </div>
            </div>
            {errors.price && <p className="text-red-400 text-xs mt-1">{errors.price}</p>}
          </div>
        )}

        {/* Amount Input */}
        <div>
          <Label className="text-gray-300 text-xs">Amount</Label>
          <div className="flex">
            <Input
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.000000"
              className="bg-slate-700 border-slate-600 text-white rounded-r-none"
              type="number"
              step="0.000001"
            />
            <div className="bg-slate-600 border border-l-0 border-slate-600 rounded-r px-3 flex items-center">
              <span className="text-gray-300 text-sm">{amountCurrency}</span>
            </div>
          </div>
          {errors.amount && <p className="text-red-400 text-xs mt-1">{errors.amount}</p>}
          
          {/* Percentage Slider */}
          <div className="mt-2">
            <div className="flex justify-between text-xs text-gray-400 mb-1">
              <span>0%</span>
              <span>{percentageAmount[0]}%</span>
              <span>100%</span>
            </div>
            <Slider
              value={percentageAmount}
              onValueChange={setPercentageAmount}
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        </div>

        {/* Total */}
        <div className="bg-slate-700 rounded p-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-300 text-sm">Total</span>
            <div className="text-right">
              <div className="text-white font-mono">
                {calculateTotal().toFixed(2)} {priceCurrency}
              </div>
              <div className="text-gray-400 text-xs">
                Minimum 5 {priceCurrency}
              </div>
            </div>
          </div>
        </div>

        {/* TP/SL Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={tpslEnabled}
                onCheckedChange={setTpslEnabled}
                className="data-[state=checked]:bg-blue-600"
              />
              <Label className="text-gray-300 text-sm">TP/SL</Label>
            </div>
          </div>

          {tpslEnabled && (
            <div className="space-y-3 p-3 bg-slate-700/50 rounded border border-slate-600">
              {/* Take Profit */}
              <div>
                <Label className="text-gray-300 text-xs">Take Profit</Label>
                <div className="grid grid-cols-2 gap-2 mt-1">
                  <div>
                    <Input
                      value={tpLimit}
                      onChange={(e) => setTpLimit(e.target.value)}
                      placeholder="TP Limit"
                      className="bg-slate-700 border-slate-600 text-white text-sm"
                      type="number"
                      step="0.00001"
                    />
                  </div>
                  <div className="flex">
                    <Input
                      value={tpOffset}
                      onChange={(e) => setTpOffset(e.target.value)}
                      placeholder="Offset"
                      className="bg-slate-700 border-slate-600 text-white text-sm rounded-r-none"
                      type="number"
                      step="0.01"
                    />
                    <div className="bg-slate-600 border border-l-0 border-slate-600 rounded-r px-2 flex items-center">
                      <Percent className="w-3 h-3 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Stop Loss */}
              <div>
                <Label className="text-gray-300 text-xs">Stop Loss</Label>
                <div className="grid grid-cols-2 gap-2 mt-1">
                  <div>
                    <Input
                      value={slTrigger}
                      onChange={(e) => setSlTrigger(e.target.value)}
                      placeholder="SL Trigger"
                      className="bg-slate-700 border-slate-600 text-white text-sm"
                      type="number"
                      step="0.00001"
                    />
                  </div>
                  <div className="flex">
                    <Input
                      value={slOffset}
                      onChange={(e) => setSlOffset(e.target.value)}
                      placeholder="Offset"
                      className="bg-slate-700 border-slate-600 text-white text-sm rounded-r-none"
                      type="number"
                      step="0.01"
                    />
                    <div className="bg-slate-600 border border-l-0 border-slate-600 rounded-r px-2 flex items-center">
                      <Percent className="w-3 h-3 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Current Price Display */}
        {currentQuote && (
          <div className="bg-slate-700/50 rounded p-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-300 text-sm">Current Price</span>
              <div className="text-right">
                <div className={`font-mono font-semibold ${
                  currentQuote.dailyChange >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatPrice(currentPrice)}
                </div>
                <div className={`text-xs ${
                  currentQuote.dailyChange >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {currentQuote.dailyChange >= 0 ? '+' : ''}{(currentQuote.dailyChange * 100).toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <Button
          onClick={handleSubmitOrder}
          disabled={isLoading}
          className={`w-full py-3 font-semibold ${
            tradeDirection === 'BUY'
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-red-600 hover:bg-red-700 text-white'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </div>
          ) : (
            `${tradeDirection === 'BUY' ? 'Buy' : 'Sell'} ${selectedSymbol}`
          )}
        </Button>

        {errors.submit && (
          <p className="text-red-400 text-sm text-center">{errors.submit}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default TradePanel;