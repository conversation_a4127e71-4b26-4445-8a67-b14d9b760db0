import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type Timeframe = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d';

export interface CurrencyPair {
  symbol: string;
  name: string;
  bid: number;
  ask: number;
  change: number;
  changePercent: number;
}

interface TradingContextType {
  selectedSymbol: string;
  selectedTimeframe: Timeframe;
  setSelectedSymbol: (symbol: string) => void;
  setSelectedTimeframe: (timeframe: Timeframe) => void;
  getCurrentPair: () => CurrencyPair | undefined;
  currencyPairs: CurrencyPair[];
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

// Mock currency pairs data with real-time simulation
const initialCurrencyPairs: CurrencyPair[] = [
  { symbol: 'EURUSD', name: 'Euro / US Dollar', bid: 1.05421, ask: 1.05439, change: 0.00087, changePercent: 0.08 },
  { symbol: 'GBPUSD', name: 'British Pound / US Dollar', bid: 1.26892, ask: 1.26911, change: -0.00142, changePercent: -0.11 },
  { symbol: 'USDJPY', name: 'US Dollar / Japanese Yen', bid: 149.87, ask: 149.91, change: 0.23, changePercent: 0.15 },
  { symbol: 'USDCHF', name: 'US Dollar / Swiss Franc', bid: 0.88745, ask: 0.88763, change: 0.00098, changePercent: 0.11 },
  { symbol: 'EURJPY', name: 'Euro / Japanese Yen', bid: 158.42, ask: 158.58, change: -0.32, changePercent: -0.20 },
  { symbol: 'EURGBP', name: 'Euro / British Pound', bid: 0.83125, ask: 0.83142, change: 0.00076, changePercent: 0.09 },
  { symbol: 'EURCHF', name: 'Euro / Swiss Franc', bid: 0.93564, ask: 0.93581, change: 0.00145, changePercent: 0.16 },
  { symbol: 'AUDUSD', name: 'Australian Dollar / US Dollar', bid: 0.65321, ask: 0.65339, change: -0.00089, changePercent: -0.14 },
];

export const TradingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedSymbol, setSelectedSymbol] = useState<string>('EURUSD');
  const [selectedTimeframe, setSelectedTimeframe] = useState<Timeframe>('1h');
  const [currencyPairs, setCurrencyPairs] = useState<CurrencyPair[]>(initialCurrencyPairs);

  // Simulate real-time price updates with more realistic market movements
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrencyPairs(prevPairs => 
        prevPairs.map(pair => {
          // Different volatility for different pairs
          let volatility = 0.0008; // Base volatility
          if (pair.symbol.includes('JPY')) volatility = 0.002;
          if (pair.symbol.includes('GBP')) volatility = 0.0012;
          if (pair.symbol.includes('AUD')) volatility = 0.0010;
          
          // Market trend simulation (slight bias)
          const trendBias = Math.sin(Date.now() / 300000) * 0.3; // 5-minute cycle
          const randomFactor = (Math.random() - 0.5) * 2;
          const direction = randomFactor + trendBias;
          
          const changeAmount = volatility * pair.bid * direction * (0.5 + Math.random() * 0.5);
          
          const newBid = Math.max(0.01, pair.bid + changeAmount);
          
          // Dynamic spread based on volatility
          const baseSpread = pair.ask - pair.bid;
          const spreadMultiplier = 1 + Math.abs(direction) * 0.1;
          const newSpread = baseSpread * spreadMultiplier;
          const newAsk = newBid + newSpread;
          
          const newChange = pair.change + changeAmount;
          const newChangePercent = (newChange / (pair.bid - pair.change)) * 100;

          return {
            ...pair,
            bid: Number(newBid.toFixed(pair.symbol.includes('JPY') ? 3 : 5)),
            ask: Number(newAsk.toFixed(pair.symbol.includes('JPY') ? 3 : 5)),
            change: Number(newChange.toFixed(pair.symbol.includes('JPY') ? 3 : 5)),
            changePercent: Number(newChangePercent.toFixed(2))
          };
        })
      );
    }, 1500); // Update every 1.5 seconds for more dynamic feel

    return () => clearInterval(interval);
  }, []);

  const getCurrentPair = (): CurrencyPair | undefined => {
    return currencyPairs.find(pair => pair.symbol === selectedSymbol);
  };

  const value: TradingContextType = {
    selectedSymbol,
    selectedTimeframe,
    setSelectedSymbol,
    setSelectedTimeframe,
    getCurrentPair,
    currencyPairs,
  };

  return (
    <TradingContext.Provider value={value}>
      {children}
    </TradingContext.Provider>
  );
};

export const useTradingContext = (): TradingContextType => {
  const context = useContext(TradingContext);
  if (context === undefined) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};