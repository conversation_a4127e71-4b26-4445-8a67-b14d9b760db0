import React from 'react';
import { messages } from '../data/mockData';
import { CheckCircle, XCircle, AlertCircle, Info, Clock } from 'lucide-react';

export const MessagesPanel: React.FC = () => {
  const getStatusIcon = (type: string) => {
    switch (type) {
      case 'filled':
        return <CheckCircle className="h-3 w-3 text-status-filled" />;
      case 'canceled':
        return <XCircle className="h-3 w-3 text-status-canceled" />;
      case 'rejected':
        return <AlertCircle className="h-3 w-3 text-status-rejected" />;
      case 'pending':
        return <Clock className="h-3 w-3 text-status-pending" />;
      default:
        return <Info className="h-3 w-3 text-status-info" />;
    }
  };

  const getStatusColor = (type: string) => {
    switch (type) {
      case 'filled':
        return 'text-status-filled';
      case 'canceled':
        return 'text-status-canceled';
      case 'rejected':
        return 'text-status-rejected';
      case 'pending':
        return 'text-status-pending';
      default:
        return 'text-status-info';
    }
  };

  return (
    <div className="h-64 panel-bg panel-border border-b">
      <div className="p-3 border-b border-trading-border">
        <h3 className="text-sm font-medium text-trading-text">Messages</h3>
      </div>
      <div className="h-52 overflow-y-auto p-2">
        <div className="space-y-2">
          {messages.map((message) => (
            <div key={message.id} className="flex items-start space-x-2 p-2 rounded hover:bg-trading-border/30">
              <div className="mt-0.5">
                {getStatusIcon(message.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className={`text-xs font-medium uppercase ${getStatusColor(message.type)}`}>
                    {message.type}
                  </span>
                  <span className="text-xs text-trading-text-secondary">
                    {message.timestamp}
                  </span>
                </div>
                <p className="text-xs text-trading-text mt-1 break-words">
                  {message.content}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};