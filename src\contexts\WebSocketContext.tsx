import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { formatPrice } from '../utils/priceFormatting';

interface Quote {
  symbol: string;
  bid: number;
  ask: number;
  spread: number;
  dailyChange: number;
  time: string;
}

interface Position {
  positionId: string;
  symbol: string;
  type: string;
  volume: number;
  openPrice: number;
  sl?: number;
  tp?: number;
  currentPrice: string;
  swap: number;
  commission: number;
  pl: number;
  openTime: string;
}

interface Order {
  orderId: string;
  symbol: string;
  type: string;
  volume: number;
  orderPrice: number;
  sl?: number;
  tp?: number;
  currentPrice: string;
  state: string;
  placementTime: string;
  expiration?: string;
}

interface Deal {
  executionTime: string;
  dealId: string;
  symbol?: string;
  type: 'balance' | 'buy' | 'sell';
  direction?: 'in' | 'out';
  volume?: number;
  executionPrice?: number;
  orderId?: string;
  commission: number;
  swap: number;
  profitLoss: number;
  balance: number;
  comment?: string;
}

interface TraderAccount {
  accountId: string;
  balance: number;
  floatingPnL: number;
  equity: number;
  currency: string;
  usedMargin: number;
  marginLevel: number;
  marginCall: number;
  stopOut: number;
}

interface ActivityEntry {
  id: string;
  timestamp: string;
  action: 'CREATE' | 'EDIT' | 'DELETE' | 'CLOSE' | 'CANCEL' | 'LOGIN' | 'LOGOUT';
  entityType: 'Position' | 'Order' | 'Deal' | 'Account' | 'System';
  entityId: string;
  details: string;
  symbol?: string;
  changes?: { field: string; from: any; to: any }[];
}

// Chart data interface for KLineChart integration
interface KLineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface WebSocketContextType {
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  quotes: Quote[];
  positions: Position[];
  orders: Order[];
  positionHistory: any[];
  orderHistory: any[];
  dealHistory: Deal[];
  traderAccount: TraderAccount;
  activityLog: ActivityEntry[];
  chartData: KLineData[];
  updatePosition: (positionId: string, updates: { sl?: number; tp?: number }) => void;
  updateOrder: (orderId: string, updates: { sl?: number; tp?: number }) => void;
  addPosition: (position: Omit<Position, 'positionId' | 'currentPrice' | 'pl'>) => void;
  addOrder: (order: Omit<Order, 'orderId' | 'currentPrice' | 'state'>) => void;
  closePosition: (positionId: string, volume: number) => void;
  cancelOrder: (orderId: string) => void;
  deletePositionHistory: (positionId: string) => Promise<void>;
  deleteOrderHistory: (orderId: string) => Promise<void>;
  deleteDealHistory: (dealId: string) => Promise<void>;
  addActivityEntry: (entry: Omit<ActivityEntry, 'id' | 'timestamp'>) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export const WebSocketProvider = ({ children }: { children: ReactNode }) => {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connecting');
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [positionHistory, setPositionHistory] = useState<any[]>([]);
  const [orderHistory, setOrderHistory] = useState<any[]>([]);
  const [dealHistory, setDealHistory] = useState<Deal[]>([]);
  const [traderAccount, setTraderAccount] = useState<TraderAccount>({} as TraderAccount);
  const [activityLog, setActivityLog] = useState<ActivityEntry[]>([]);
  const [chartData, setChartData] = useState<KLineData[]>([]);

  // Current trader info (simplified from dealer context)
  const currentTrader = {
    traderId: 'TRADER001',
    sessionId: 'SESSION_' + Date.now()
  };

  const addActivityEntry = (entry: Omit<ActivityEntry, 'id' | 'timestamp'>) => {
    const activityEntry: ActivityEntry = {
      ...entry,
      id: 'ACTIVITY_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString()
    };
    
    setActivityLog(prev => [activityEntry, ...prev]);
  };

  const updatePosition = (positionId: string, updates: { sl?: number; tp?: number }) => {
    setPositions(prevPositions => {
      const oldPosition = prevPositions.find(p => p.positionId === positionId);
      if (!oldPosition) return prevPositions;

      const changes: { field: string; from: any; to: any }[] = [];
      if (updates.sl !== undefined && updates.sl !== oldPosition.sl) {
        changes.push({ field: 'Stop Loss', from: oldPosition.sl || 'None', to: updates.sl });
      }
      if (updates.tp !== undefined && updates.tp !== oldPosition.tp) {
        changes.push({ field: 'Take Profit', from: oldPosition.tp || 'None', to: updates.tp });
      }

      if (changes.length > 0) {
        addActivityEntry({
          action: 'EDIT',
          entityType: 'Position',
          entityId: positionId,
          symbol: oldPosition.symbol,
          details: `Modified position ${positionId}: ${changes.map(c => `${c.field} changed from ${c.from} to ${c.to}`).join(', ')}`,
          changes
        });
      }

      return prevPositions.map(position =>
        position.positionId === positionId
          ? { ...position, ...updates }
          : position
      );
    });
  };

  const updateOrder = (orderId: string, updates: { sl?: number; tp?: number }) => {
    setOrders(prevOrders => {
      const oldOrder = prevOrders.find(o => o.orderId === orderId);
      if (!oldOrder) return prevOrders;

      const changes: { field: string; from: any; to: any }[] = [];
      if (updates.sl !== undefined && updates.sl !== oldOrder.sl) {
        changes.push({ field: 'Stop Loss', from: oldOrder.sl || 'None', to: updates.sl });
      }
      if (updates.tp !== undefined && updates.tp !== oldOrder.tp) {
        changes.push({ field: 'Take Profit', from: oldOrder.tp || 'None', to: updates.tp });
      }

      if (changes.length > 0) {
        addActivityEntry({
          action: 'EDIT',
          entityType: 'Order',
          entityId: orderId,
          symbol: oldOrder.symbol,
          details: `Modified order ${orderId}: ${changes.map(c => `${c.field} changed from ${c.from} to ${c.to}`).join(', ')}`,
          changes
        });
      }

      return prevOrders.map(order =>
        order.orderId === orderId
          ? { ...order, ...updates }
          : order
      );
    });
  };

  const closePosition = (positionId: string, volume: number) => {
    setPositions(prevPositions => {
      const positionToClose = prevPositions.find(p => p.positionId === positionId);
      if (!positionToClose) return prevPositions;

      // Add to history
      const historyRecord = {
        ...positionToClose,
        closeTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        closedVolume: volume,
        status: 'CLOSED',
        finalPL: positionToClose.pl
      };
      setPositionHistory(prev => [historyRecord, ...prev]);

      // Create deal record for position closing
      const closeDeal: Deal = {
        executionTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        dealId: `DEAL${Date.now().toString().slice(-6)}`,
        symbol: positionToClose.symbol,
        type: positionToClose.type === 'BUY' ? 'sell' : 'buy',
        direction: 'out',
        volume: volume,
        executionPrice: parseFloat(positionToClose.currentPrice),
        orderId: positionToClose.positionId,
        commission: volume === positionToClose.volume ? positionToClose.commission : (positionToClose.commission * (volume / positionToClose.volume)),
        swap: volume === positionToClose.volume ? positionToClose.swap : (positionToClose.swap * (volume / positionToClose.volume)),
        profitLoss: volume === positionToClose.volume ? positionToClose.pl : (positionToClose.pl * (volume / positionToClose.volume)),
        balance: traderAccount.balance + positionToClose.pl
      };

      setDealHistory(prevDeals => [closeDeal, ...prevDeals]);

      // Add activity entry
      addActivityEntry({
        action: 'CLOSE',
        entityType: 'Position',
        entityId: positionId,
        symbol: positionToClose.symbol,
        details: `Closed position ${positionId} - ${positionToClose.symbol} ${positionToClose.type} ${volume} lots at ${positionToClose.currentPrice}, P/L: ${positionToClose.pl >= 0 ? '+' : ''}$${positionToClose.pl.toFixed(2)}`
      });

      // If partial close, update volume; if full close, remove position
      if (volume >= positionToClose.volume) {
        return prevPositions.filter(p => p.positionId !== positionId);
      } else {
        return prevPositions.map(p =>
          p.positionId === positionId
            ? { ...p, volume: p.volume - volume }
            : p
        );
      }
    });
  };

  const cancelOrder = (orderId: string) => {
    setOrders(prevOrders => {
      const orderToCancel = prevOrders.find(o => o.orderId === orderId);
      if (!orderToCancel) return prevOrders;

      // Add to history
      const historyRecord = {
        ...orderToCancel,
        cancelTime: new Date().toLocaleString(),
        status: 'CANCELLED'
      };
      setOrderHistory(prev => [historyRecord, ...prev]);

      // Add activity entry
      addActivityEntry({
        action: 'CANCEL',
        entityType: 'Order',
        entityId: orderId,
        symbol: orderToCancel.symbol,
        details: `Cancelled order ${orderId} - ${orderToCancel.symbol} ${orderToCancel.type} ${orderToCancel.volume} lots at ${orderToCancel.orderPrice}`
      });

      return prevOrders.filter(o => o.orderId !== orderId);
    });
  };

  const deletePositionHistory = async (positionId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setPositionHistory(prevHistory => {
        const recordToDelete = prevHistory.find(p => p.positionId === positionId);
        if (!recordToDelete) {
          reject(new Error('Position record not found'));
          return prevHistory;
        }

        // Add activity entry
        addActivityEntry({
          action: 'DELETE',
          entityType: 'Position',
          entityId: positionId,
          symbol: recordToDelete.symbol,
          details: `DELETED position history record ${positionId} - ${recordToDelete.symbol} ${recordToDelete.type} ${recordToDelete.volume} lots, Final P/L: ${recordToDelete.finalPL >= 0 ? '+' : ''}$${recordToDelete.finalPL.toFixed(2)}`
        });

        resolve();
        return prevHistory.filter(p => p.positionId !== positionId);
      });
    });
  };

  const deleteOrderHistory = async (orderId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setOrderHistory(prevHistory => {
        const recordToDelete = prevHistory.find(o => o.orderId === orderId);
        if (!recordToDelete) {
          reject(new Error('Order record not found'));
          return prevHistory;
        }

        // Add activity entry
        addActivityEntry({
          action: 'DELETE',
          entityType: 'Order',
          entityId: orderId,
          symbol: recordToDelete.symbol,
          details: `DELETED order history record ${orderId} - ${recordToDelete.symbol} ${recordToDelete.type} ${recordToDelete.volume} lots at ${recordToDelete.orderPrice}`
        });

        resolve();
        return prevHistory.filter(o => o.orderId !== orderId);
      });
    });
  };

  const deleteDealHistory = async (dealId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setDealHistory(prevHistory => {
        const recordToDelete = prevHistory.find(d => d.dealId === dealId);
        if (!recordToDelete) {
          reject(new Error('Deal record not found'));
          return prevHistory;
        }

        // Add activity entry
        addActivityEntry({
          action: 'DELETE',
          entityType: 'Deal',
          entityId: dealId,
          symbol: recordToDelete.symbol,
          details: `DELETED deal history record ${dealId} - ${recordToDelete.symbol} ${recordToDelete.type} ${recordToDelete.volume} lots at ${recordToDelete.executionPrice}`
        });

        resolve();
        return prevHistory.filter(d => d.dealId !== dealId);
      });
    });
  };

  const addPosition = (position: Omit<Position, 'positionId' | 'currentPrice' | 'pl'>) => {
    const newPosition: Position = {
      ...position,
      positionId: `POS${Date.now().toString().slice(-6)}`,
      currentPrice: '0.00000',
      pl: 0.00
    };

    setPositions(prevPositions => [newPosition, ...prevPositions]);

    // Create corresponding deal record
    const newDeal: Deal = {
      executionTime: position.openTime,
      dealId: `DEAL${Date.now().toString().slice(-6)}`,
      symbol: position.symbol,
      type: position.type.toLowerCase() as 'buy' | 'sell',
      direction: 'in',
      volume: position.volume,
      executionPrice: position.openPrice,
      orderId: newPosition.positionId,
      commission: position.commission,
      swap: position.swap,
      profitLoss: 0.00,
      balance: traderAccount.balance || 10000.00
    };

    setDealHistory(prevDeals => [newDeal, ...prevDeals]);

    // Add activity entry
    addActivityEntry({
      action: 'CREATE',
      entityType: 'Position',
      entityId: newPosition.positionId,
      symbol: newPosition.symbol,
      details: `Created new position ${newPosition.positionId} - ${newPosition.symbol} ${newPosition.type} ${newPosition.volume} lots at ${newPosition.openPrice}${newPosition.sl ? `, SL: ${newPosition.sl}` : ''}${newPosition.tp ? `, TP: ${newPosition.tp}` : ''}`
    });
  };

  const addOrder = (order: Omit<Order, 'orderId' | 'currentPrice' | 'state'>) => {
    const newOrder: Order = {
      ...order,
      orderId: `ORD${Date.now().toString().slice(-6)}`,
      currentPrice: '0.00000',
      state: 'PENDING'
    };

    setOrders(prevOrders => [newOrder, ...prevOrders]);

    // Add activity entry
    addActivityEntry({
      action: 'CREATE',
      entityType: 'Order',
      entityId: newOrder.orderId,
      symbol: newOrder.symbol,
      details: `Created new order ${newOrder.orderId} - ${newOrder.symbol} ${newOrder.type} ${newOrder.volume} lots at ${newOrder.orderPrice}${newOrder.sl ? `, SL: ${newOrder.sl}` : ''}${newOrder.tp ? `, TP: ${newOrder.tp}` : ''}`
    });
  };

  useEffect(() => {
    // Simulate WebSocket connection
    const connectWebSocket = () => {
      setConnectionStatus('connecting');
      
      // Simulate connection delay
      setTimeout(() => {
        setConnectionStatus('connected');
        
        // Initialize mock data for personal trader account
        const mockQuotes: Quote[] = [
          { symbol: 'EURUSD', bid: 1.08547, ask: 1.08564, spread: 0.2, dailyChange: 0.0012, time: new Date().toLocaleTimeString() },
          { symbol: 'GBPUSD', bid: 1.26396, ask: 1.26339, spread: 0.3, dailyChange: -0.0008, time: new Date().toLocaleTimeString() },
          { symbol: 'USDJPY', bid: 149.82009, ask: 149.85070, spread: 0.3, dailyChange: 0.0045, time: new Date().toLocaleTimeString() },
          { symbol: 'AUDUSD', bid: 0.65369, ask: 0.65499, spread: 0.3, dailyChange: 0.0023, time: 'STALE_DATA' },
          { symbol: 'USDCAD', bid: 1.36811, ask: 1.36865, spread: 0.3, dailyChange: -0.0015, time: new Date().toLocaleTimeString() },
          { symbol: 'XAUUSD', bid: 2052.45, ask: 2053.25, spread: 0.8, dailyChange: 0.0145, time: new Date().toLocaleTimeString() },
          { symbol: 'XAGUSD', bid: 24.123, ask: 24.145, spread: 0.22, dailyChange: -0.0089, time: new Date().toLocaleTimeString() },
          { symbol: 'BTCUSD', bid: 43567.2, ask: 43589.8, spread: 22.6, dailyChange: 0.0234, time: new Date().toLocaleTimeString() },
          { symbol: 'ETHUSD', bid: 2547.85, ask: 2549.12, spread: 1.27, dailyChange: 0.0189, time: new Date().toLocaleTimeString() },
          { symbol: 'EURJPY', bid: 162.445, ask: 162.520, spread: 0.75, dailyChange: 0.0031, time: new Date().toLocaleTimeString() },
          { symbol: 'GBPJPY', bid: 189.234, ask: 189.315, spread: 0.81, dailyChange: -0.0022, time: new Date().toLocaleTimeString() },
        ];
        
        // Personal trader positions (simplified from multi-account)
        const mockPositions: Position[] = [
          {
            positionId: 'POS001',
            symbol: 'EURUSD',
            type: 'BUY',
            volume: 1.0,
            openPrice: 1.0835,
            sl: 1.0800,
            tp: 1.0900,
            currentPrice: '1.0847',
            swap: -2.45,
            commission: -8.50,
            pl: 120.00,
            openTime: '2024-06-19 09:15:23'
          },
          {
            positionId: 'POS002',
            symbol: 'GBPUSD',
            type: 'SELL',
            volume: 0.5,
            openPrice: 1.2650,
            sl: 1.2700,
            tp: 1.2600,
            currentPrice: '1.2634',
            swap: 1.25,
            commission: -4.25,
            pl: 80.00,
            openTime: '2024-06-19 10:30:15'
          },
          {
            positionId: 'POS003',
            symbol: 'USDJPY',
            type: 'BUY',
            volume: 2.0,
            openPrice: 149.50,
            sl: 148.80,
            tp: 151.00,
            currentPrice: '149.82',
            swap: -1.20,
            commission: -12.00,
            pl: 640.00,
            openTime: '2024-06-19 11:45:10'
          }
        ];
        
        const mockOrders: Order[] = [
          {
            orderId: 'ORD001',
            symbol: 'EURUSD',
            type: 'BUY LIMIT',
            volume: 0.5,
            orderPrice: 1.0800,
            sl: 1.0750,
            tp: 1.0850,
            currentPrice: '1.0847',
            state: 'PENDING',
            placementTime: '2024-06-19 11:00:00',
            expiration: '2024-06-20 11:00:00'
          },
          {
            orderId: 'ORD002',
            symbol: 'GBPUSD',
            type: 'SELL STOP',
            volume: 0.3,
            orderPrice: 1.2600,
            sl: 1.2650,
            tp: 1.2550,
            currentPrice: '1.2634',
            state: 'PENDING',
            placementTime: '2024-06-19 12:00:00'
          }
        ];
        
        // Personal trader account (single account)
        const mockTraderAccount: TraderAccount = {
          accountId: 'ACC001',
          balance: 50000.00,
          floatingPnL: 840.00,
          equity: 50840.00,
          currency: 'USD',
          usedMargin: 8420.50,
          marginLevel: 603.2,
          marginCall: 100.0,
          stopOut: 50.0
        };
        
        // Sample chart data for KLineChart
        const mockChartData: KLineData[] = [
          { timestamp: Date.now() - 600000, open: 1.0835, high: 1.0845, low: 1.0825, close: 1.0840, volume: 1000 },
          { timestamp: Date.now() - 540000, open: 1.0840, high: 1.0850, low: 1.0830, close: 1.0845, volume: 1200 },
          { timestamp: Date.now() - 480000, open: 1.0845, high: 1.0855, low: 1.0835, close: 1.0847, volume: 900 },
          { timestamp: Date.now() - 420000, open: 1.0847, high: 1.0857, low: 1.0837, close: 1.0850, volume: 1100 },
          { timestamp: Date.now() - 360000, open: 1.0850, high: 1.0860, low: 1.0840, close: 1.0855, volume: 1300 }
        ];

        setQuotes(mockQuotes);
        setPositions(mockPositions);
        setOrders(mockOrders);
        setTraderAccount(mockTraderAccount);
        setChartData(mockChartData);
        
        // Create LOGIN activity record for current session
        const loginActivityEntry: ActivityEntry = {
          id: 'ACTIVITY_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
          timestamp: new Date().toISOString(),
          action: 'LOGIN',
          entityType: 'System',
          entityId: currentTrader.sessionId,
          details: `Trader ${currentTrader.traderId} logged into Nexus Trader Terminal from Windows Workstation (Current Session)`
        };
        
        // Add the login record to the top of the activity log
        setActivityLog(prev => [loginActivityEntry, ...prev]);
        
        // Simulate real-time updates
        const updateQuotes = () => {
          setQuotes(prevQuotes => {
            const updatedQuotes = prevQuotes.map(quote => {
              // Define different volatility ranges based on asset type
              let volatility = 0.0001; // Default forex volatility
              
              if (quote.symbol.includes('XAU') || quote.symbol.includes('XAG')) {
                volatility = quote.symbol === 'XAUUSD' ? 0.5 : 0.02;
              } else if (quote.symbol.includes('BTC') || quote.symbol.includes('ETH')) {
                volatility = quote.symbol === 'BTCUSD' ? 25 : 2;
              } else if (quote.symbol.includes('JPY')) {
                volatility = 0.01;
              }
              
              return {
                ...quote,
                bid: quote.bid + (Math.random() - 0.5) * volatility,
                ask: quote.ask + (Math.random() - 0.5) * volatility,
                time: quote.symbol === 'AUDUSD' ? 'STALE_DATA' : new Date().toLocaleTimeString()
              };
            });

            // Update positions with new current prices and recalculate P/L
            setPositions(prevPositions => 
              prevPositions.map(position => {
                const matchingQuote = updatedQuotes.find(q => q.symbol === position.symbol);
                if (matchingQuote) {
                  const currentPrice = position.type === 'BUY' ? matchingQuote.bid : matchingQuote.ask;
                  const priceDiff = position.type === 'BUY' 
                    ? currentPrice - position.openPrice 
                    : position.openPrice - currentPrice;
                  const pl = (priceDiff * position.volume * 100000) + position.swap + position.commission;
                  
                  return {
                    ...position,
                    currentPrice: formatPrice(currentPrice, position.symbol),
                    pl: parseFloat(pl.toFixed(2))
                  };
                }
                return position;
              })
            );

            // Update orders with new current prices
            setOrders(prevOrders => 
              prevOrders.map(order => {
                const matchingQuote = updatedQuotes.find(q => q.symbol === order.symbol);
                if (matchingQuote) {
                  const currentPrice = order.type.includes('BUY') ? matchingQuote.ask : matchingQuote.bid;
                  return {
                    ...order,
                    currentPrice: formatPrice(currentPrice, order.symbol)
                  };
                }
                return order;
              })
            );

            // Update trader account equity based on P/L changes
            setTraderAccount(prevAccount => {
              const totalPL = positions.reduce((sum, pos) => sum + pos.pl, 0);
              return {
                ...prevAccount,
                floatingPnL: totalPL,
                equity: prevAccount.balance + totalPL
              };
            });

            return updatedQuotes;
          });
        };
        
        const interval = setInterval(updateQuotes, 1000);
        
        return () => clearInterval(interval);
      }, 2000);
    };

    connectWebSocket();
  }, []);

  return (
    <WebSocketContext.Provider value={{ 
      connectionStatus, 
      quotes, 
      positions, 
      orders, 
      positionHistory,
      orderHistory,
      dealHistory,
      traderAccount,
      activityLog,
      chartData,
      updatePosition,
      updateOrder,
      closePosition,
      cancelOrder,
      deletePositionHistory,
      deleteOrderHistory,
      deleteDealHistory,
      addActivityEntry,
      addPosition,
      addOrder
    }}>
      {children}
    </WebSocketContext.Provider>
  );
};