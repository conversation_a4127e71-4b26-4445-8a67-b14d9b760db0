import React, { useState } from 'react';
import { useTradingContext } from '../contexts/TradingContext';

export const OrderTicket: React.FC = () => {
  const { getCurrentPair } = useTradingContext();
  const [quantity, setQuantity] = useState('1.0');
  const currentPair = getCurrentPair();

  if (!currentPair) return null;

  return (
    <div className="h-24 panel-bg panel-border p-3">
      <div className="flex items-center justify-between h-full space-x-2">
        {/* Sell Button */}
        <button className="flex-1 h-full bg-button-sell hover:bg-button-sell-hover text-white rounded font-medium transition-colors flex flex-col items-center justify-center">
          <div className="text-xs mb-1">SELL</div>
          <div className="text-sm font-bold">{currentPair.bid.toFixed(5)}</div>
        </button>

        {/* Quantity Input */}
        <div className="w-16">
          <input
            type="text"
            value={quantity}
            onChange={(e) => setQuantity(e.target.value)}
            className="w-full h-12 text-center text-sm border border-trading-border rounded bg-trading-panel text-trading-text focus:outline-none focus:ring-2 focus:ring-accent"
          />
        </div>

        {/* Buy Button */}
        <button className="flex-1 h-full bg-button-buy hover:bg-button-buy-hover text-white rounded font-medium transition-colors flex flex-col items-center justify-center">
          <div className="text-xs mb-1">BUY</div>
          <div className="text-sm font-bold">{currentPair.ask.toFixed(5)}</div>
        </button>
      </div>
    </div>
  );
};