import React, { useEffect, useState } from 'react';
import { Moon, Sun } from 'lucide-react';
import { TradingProvider } from '../contexts/TradingContext';
import { TradingHeader } from '../components/TradingHeader';
import { MiniChart } from '../components/MiniChart';
import { Watchlist } from '../components/Watchlist';
import { OrderTicket } from '../components/OrderTicket';
import { MainChart } from '../components/MainChart';
import { MessagesPanel } from '../components/MessagesPanel';
import { PositionsPanel } from '../components/PositionsPanel';
import { BottomPanel } from '../components/BottomPanel';

const Index = () => {
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark mode for trading

  useEffect(() => {
    // Apply dark mode class to html element
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };
  return (
    <TradingProvider>
      <div className="h-screen bg-trading-bg text-trading-text flex flex-col overflow-hidden">
        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className="fixed top-4 right-4 z-50 p-2 bg-trading-panel border border-trading-border rounded-lg shadow-lg hover:bg-trading-border/50 transition-colors"
          title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
        >
          {isDarkMode ? (
            <Sun className="h-4 w-4 text-trading-text" />
          ) : (
            <Moon className="h-4 w-4 text-trading-text" />
          )}
        </button>
        
        {/* Header */}
        <TradingHeader />
        
        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Column */}
          <div className="w-64 flex flex-col">
            <MiniChart />
            <Watchlist />
            <OrderTicket />
          </div>
          
          {/* Center Column - Main Chart */}
          <div className="flex-1">
            <MainChart />
          </div>
          
          {/* Right Column */}
          <div className="w-80 flex flex-col">
            <MessagesPanel />
            <PositionsPanel />
          </div>
        </div>
        
        {/* Bottom Panel */}
        <BottomPanel />
      </div>
    </TradingProvider>
  );
};

export default Index;