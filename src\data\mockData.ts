import { CurrencyPair, Order, Position, Message, AccountSummary, ChartDataPoint } from '../types/trading';

export const currencyPairs: CurrencyPair[] = [
  { symbol: 'EUR/CAD', name: 'Euro / Canadian Dollar', bid: 1.35655, ask: 1.35655, change: 0.109, changePercent: 0.109 },
  { symbol: 'EUR/RUB', name: 'Euro / Russian Ruble', bid: 61.8684, ask: 61.8736, change: 0.762, changePercent: 0.762 },
  { symbol: 'EUR/CHF', name: 'Euro / Swiss Franc', bid: 1.04102, ask: 1.04135, change: 0.239, changePercent: 0.239 },
  { symbol: 'EUR/GBP', name: 'Euro / British Pound', bid: 0.72660, ask: 0.72795, change: 1.334, changePercent: 1.334 },
  { symbol: 'EUR/CNY', name: 'Euro / Chinese Yuan', bid: 6.68571, ask: 6.68789, change: 0.376, changePercent: 0.376 },
  { symbol: 'EUR/JPY', name: 'Euro / Japanese Yen', bid: 128.837, ask: 128.950, change: 0.000, changePercent: 0.000 },
  { symbol: 'EUR/AUD', name: 'Euro / Australian Dollar', bid: 1.41504, ask: 1.41596, change: -0.684, changePercent: -0.684 },
  { symbol: 'EUR/USD', name: 'Euro / US Dollar', bid: 1.07552, ask: 1.07992, change: 1.052, changePercent: 1.052 },
  { symbol: 'USD/CAD', name: 'US Dollar / Canadian Dollar', bid: 1.24458, ask: 1.24492, change: -0.680, changePercent: -0.680 },
  { symbol: 'USD/RUB', name: 'US Dollar / Russian Ruble', bid: 57.4755, ask: 57.4817, change: 1.126, changePercent: 1.126 },
  { symbol: 'USD/CHF', name: 'US Dollar / Swiss Franc', bid: 0.96620, ask: 0.96673, change: 0.291, changePercent: 0.291 },
  { symbol: 'USD/GBP', name: 'US Dollar / British Pound', bid: 0.67415, ask: 0.67538, change: -0.165, changePercent: -0.165 },
  { symbol: 'USD/TRY', name: 'US Dollar / Turkish Lira', bid: 2.59885, ask: 2.59965, change: 0.102, changePercent: 0.102 },
  { symbol: 'USD/JPY', name: 'US Dollar / Japanese Yen', bid: 119.615, ask: 119.678, change: 0.312, changePercent: 0.312 },
  { symbol: 'USD/AUD', name: 'US Dollar / Australian Dollar', bid: 1.31380, ask: 1.31445, change: -0.202, changePercent: -0.202 },
  { symbol: 'USD/NOK', name: 'US Dollar / Norwegian Krone', bid: 8.01558, ask: 8.01641, change: 0.652, changePercent: 0.652 },
  { symbol: 'USD/SEK', name: 'US Dollar / Swedish Krona', bid: 8.64472, ask: 8.64550, change: 0.520, changePercent: 0.520 },
  { symbol: 'USD/DKK', name: 'US Dollar / Danish Krone', bid: 6.85808, ask: 6.85891, change: -0.015, changePercent: -0.015 },
  { symbol: 'USD/PLN', name: 'US Dollar / Polish Zloty', bid: 3.73526, ask: 3.73652, change: -1.074, changePercent: -1.074 }
];

export const accountSummary: AccountSummary = {
  balance: 997416,
  equity: 998283,
  fpl: 867,
  usedMargin: 122912,
  usableMargin: 875371,
  account: '1303',
  currency: 'USD',
  user: 'fh-*************'
};

export const orders: Order[] = [
  {
    id: '*********:134',
    status: 'working',
    symbol: 'EUR/NZD',
    side: 'sell',
    quantity: 10,
    price: 1.32985,
    type: 'L',
    stopLoss: 1.33500,
    takeProfit: 1.32500,
    fillPrice: 1.32950,
    bid: 1.33042,
    ask: 1.33042,
    timestamp: '01.24 03:23:39'
  },
  {
    id: '*********:133',
    status: 'pending',
    symbol: 'EUR/JPY',
    side: 'sell',
    quantity: 20,
    price: 128.845,
    type: 'L',
    stopLoss: 129.500,
    takeProfit: 128.500,
    fillPrice: 128.837,
    bid: 128.950,
    ask: 128.950,
    timestamp: '01.24 03:23:17'
  },
  {
    id: '*********:132',
    status: 'working',
    symbol: 'EUR/CAD',
    side: 'buy',
    quantity: 100,
    price: 1.35555,
    type: 'S',
    stopLoss: 1.35500,
    takeProfit: 1.36500,
    fillPrice: 1.26058,
    bid: 1.26110,
    ask: 1.26110,
    timestamp: '01.24 03:23:12'
  },
  {
    id: '*********:131',
    status: 'working',
    symbol: 'USD/CAD',
    side: 'buy',
    quantity: 100,
    price: 0.96525,
    type: 'S',
    stopLoss: 0.96000,
    takeProfit: 0.97000,
    fillPrice: 0.96520,
    bid: 0.96673,
    ask: 0.96673,
    timestamp: '01.24 03:23:10'
  },
  {
    id: '*********:130',
    status: 'canceled',
    symbol: 'USD/CHF',
    side: 'sell',
    quantity: 665,
    price: 0.96630,
    type: 'TrS',
    stopLoss: 0.96000,
    takeProfit: 0.96000,
    fillPrice: 0.96615,
    bid: 0.96689,
    ask: 0.96689,
    timestamp: '01.24 03:21:04'
  }
];

export const positions: Position[] = [
  { symbol: 'EUR/USD', side: 'buy', quantity: 150, profitLoss: 1569.64, profitLossPercent: 1.569 },
  { symbol: 'EUR/CAD', side: 'buy', quantity: 210, profitLoss: 112.78, profitLossPercent: 0.112 },
  { symbol: 'EUR/HUF', side: 'sell', quantity: 76, profitLoss: 156.81, profitLossPercent: 0.156 },
  { symbol: 'EUR/CHF', side: 'sell', quantity: 57, profitLoss: 1400.12, profitLossPercent: 1.400 },
  { symbol: 'EUR/GBP', side: 'buy', quantity: 188, profitLoss: -1.01, profitLossPercent: -0.001 },
  { symbol: 'EUR/CNY', side: 'sell', quantity: 300, profitLoss: 490.43, profitLossPercent: 0.490 },
  { symbol: 'EUR/JPY', side: 'buy', quantity: 21, profitLoss: -112.78, profitLossPercent: -0.112 },
  { symbol: 'EUR/AUD', side: 'sell', quantity: 68, profitLoss: 156.81, profitLossPercent: 0.156 },
  { symbol: 'EUR/USD', side: 'buy', quantity: 19, profitLoss: 140.12, profitLossPercent: 0.140 },
  { symbol: 'EUR/TRL', side: 'sell', quantity: 100, profitLoss: -42.80, profitLossPercent: -0.042 }
];

export const messages: Message[] = [
  {
    id: '1',
    type: 'canceled',
    content: 'Order #419767632 SELL 100K EUR/USD @ 1.07149 Limit',
    timestamp: '09:31:01',
    orderId: '#419767632'
  },
  {
    id: '2',
    type: 'filled',
    content: 'Order #419767755 BUY 100K EUR/USD @ 1.07107 Limit SL: 1.07000 TP: —',
    timestamp: '08:14:23',
    orderId: '#419767755'
  },
  {
    id: '3',
    type: 'info',
    content: 'Market is opened. Working hours: Mon 00:00 — Fri 23:59',
    timestamp: '00:00:00'
  },
  {
    id: '4',
    type: 'rejected',
    content: 'Order #419767732 BUY 50K GBP/USD @ 1.46341 Market',
    timestamp: '22:38:05',
    orderId: '#419767732'
  },
  {
    id: '5',
    type: 'canceled',
    content: 'Order #419767725 BUY 10K AUD/USD @ 0.76820 Stop',
    timestamp: '22:38:05',
    orderId: '#419767725'
  }
];

export const generateChartData = (symbol: string, timeframe: string): ChartDataPoint[] => {
  const basePrice = currencyPairs.find(p => p.symbol === symbol)?.bid || 1.0;
  const points: ChartDataPoint[] = [];
  const now = new Date();
  
  for (let i = 199; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 15 * 60 * 1000); // 15-minute intervals
    const random = Math.random() * 0.002 - 0.001; // ±0.1% volatility
    const open = basePrice + random;
    const high = open + Math.random() * 0.001;
    const low = open - Math.random() * 0.001;
    const close = low + Math.random() * (high - low);
    const volume = Math.floor(Math.random() * 2000) + 500;
    
    points.push({
      time: time.toISOString(),
      open,
      high,
      low,
      close,
      volume
    });
  }
  
  return points;
};