# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Plan & Preview

### Before starting work
- Always in plan mode to make a plan
- After get the plan, make sure you Write the plan to docs/PLANNING.md
- The plan should be a detailed implementation plan and the reasoning behind them, as well as tasks broken down in docs/TASKS.md
- If the task require external knowledge or certain package, also research to get latest knowledge (Use Task tool for research)
- Don't over plan it, always think MVP.
- Once you write the plan, firstly ask me to review it. Do not continue until I approve the plan.

### While implementing
- You should update the plan as you work.
- After you complete tasks in the plan, you should update and append detailed descriptions of the changes you made, so following tasks can be easily hand over to other engineers.

## Project Overview

**Nexus Trader Terminal** is a professional personal trading terminal built as a React single-page application. It provides real-time trading capabilities, position management, advanced charting with technical analysis, and secure authentication for individual traders.

**Key Transformation**: This codebase was converted from "OTX Dealer Terminal Pro" (multi-account dealer management) to "Nexus Trader Terminal" (single personal trader account) with comprehensive KLineChart integration.

## Development Commands

```bash
# Development
npm install          # Install dependencies
npm run dev          # Start dev server (auto-detects available port, typically 8080+)
npm run build        # Production build  
npm run build:dev    # Development build with dev mode
npm run lint         # Run ESLint (flat config with React Hooks + TypeScript)
npm run preview      # Preview production build locally

# Alternative package manager
bun install          # Alternative to npm install (lockfiles for both npm/bun available)

# Testing (currently no test scripts configured - add as needed)
# npm test            # Run tests (not yet configured)
# npm run test:watch  # Run tests in watch mode (not yet configured)
```

## Architecture

### Core Stack
- **React 18.3.1** with TypeScript and **Vite 5.4.1** (React SWC plugin for fast compilation)
- **shadcn/ui** component library (40+ pre-built Radix UI components)
- **KLineChart v10.0.0-alpha5** for professional trading charts with technical indicators
- **TanStack React Query v5** for server state management and caching
- **React Router DOM v6** for client-side routing (simplified single route structure)
- **Tailwind CSS v3** with custom design system and dark theme
- **React Hook Form + Zod** for form validation
- **Google reCAPTCHA v2** for authentication security

### State Management Pattern
- **AuthContext** (`src/contexts/AuthContext.tsx`) - Single trader authentication, CAPTCHA validation, and rate limiting
- **WebSocketContext** (`src/contexts/WebSocketContext.tsx`) - Personal trading account data, real-time quotes, positions, orders, and chart data
- **React Query** for server state caching, synchronization, and background updates
- **TypeScript interfaces** (`src/types/trading.ts`) - Comprehensive type definitions for single-account trading

### Real-Time Data Management
The WebSocketContext provides mock real-time data simulation focused on personal trading:
- Single trader account with personal P&L tracking
- Live price quote updates every 1 second with realistic volatility
- Personal position and order management without multi-account complexity
- Chart data generation (`KLineData[]`) for real-time chart updates
- Personal activity logging (replacing multi-user audit system)
- Mock trader account data with margin levels and equity calculations

### Key Components Structure
```
src/
├── components/
│   ├── ui/                    # shadcn/ui components (Button, Card, Input, Table, SortableTable, etc.)
│   ├── modals/                # Trading operation modals (NewOrderModal, ModifyPositionModal, etc.)
│   ├── tabs/                  # Data view tabs (OpenPositionsTab, PendingOrdersTab, ActivityLogTab)
│   ├── Dashboard.tsx          # Main trading interface with resizable panels
│   ├── TradingChart.tsx       # Professional KLineChart component with technical indicators
│   ├── PersonalStatsPanel.tsx # Personal P&L and trading statistics
│   ├── AccountInfoPanel.tsx   # Single account information and margin details
│   ├── PriceQuotesPanel.tsx   # Live quotes with chart modal integration
│   ├── Header.tsx             # "Nexus Trader Terminal" branding
│   └── LoginPage.tsx          # Simple trader authentication
├── contexts/                  # React contexts for global state management
├── types/                     # TypeScript type definitions (single-account focused)
├── utils/                     # Utility functions (price formatting, etc.)
└── hooks/                     # Custom React hooks (use-mobile, custom toast hooks)
```

### Component Architecture Patterns
- **Single Account Focus**: All components designed for personal trader use (no multi-account complexity)
- **Professional Charting**: KLineChart integration with modal display and technical indicators
- **Panel-based Layout**: Main Dashboard uses resizable panels for optimal trading workspace
- **Modal-driven Operations**: All trading actions use modal dialogs for better UX
- **Context Menu Integration**: Right-click context menus for chart access and trading operations
- **Real-time Updates**: Components automatically re-render with WebSocket data changes

### Chart Integration Architecture
- **TradingChart Component**: Professional KLineChart integration with technical indicators
- **Chart Data Flow**: WebSocketContext generates `KLineData[]` from live quotes
- **Technical Indicators**: MA, EMA, RSI, MACD with configurable timeframes
- **Modal Display**: Charts open in full-screen modals from price quotes context menu
- **Real-time Updates**: Live price data automatically updates chart candlesticks
- **Timeframe Support**: 1m, 5m, 15m, 30m, 1h, 4h, 1d with data aggregation

## Security Implementation

The application implements Google reCAPTCHA v2 with comprehensive security features:

- **Personal Authentication**: Simple trader login (trader01/password123 for demo)
- **CAPTCHA Integration**: Required for all authentication attempts
- **Rate Limiting**: Built into AuthContext with lockout mechanisms
- **Environment Configuration**: `.env` file with reCAPTCHA site key required

## Trading Terminal Features

### Core Personal Trading Functionality
- **Personal Account Management**: Single trader account with real-time balance and equity
- **Position Management**: Open positions with live P&L tracking
- **Order Management**: Pending orders with modification capabilities  
- **Live Price Quotes**: Real-time market data with chart integration
- **Personal Activity Log**: Individual trading activity tracking
- **Advanced Charting**: Professional KLineChart with technical analysis tools

### Chart Features
- **Professional Indicators**: Moving Averages, RSI, MACD, Volume indicators
- **Multiple Timeframes**: 1-minute to daily charts with real-time data
- **Technical Analysis**: Full charting capabilities optimized for trading
- **Modal Integration**: Charts accessible via context menus from price quotes

### Modal-Driven Workflows
Trading operations use modal dialogs for:
- Creating new orders
- Modifying existing positions
- Closing positions
- Managing pending orders
- Viewing advanced charts

## Development Patterns

### Component Development
- Use existing shadcn/ui components from `src/components/ui/`
- Follow single-account patterns (no multi-account complexity)
- Implement responsive design with Tailwind CSS utilities
- Use React Hook Form with Zod validation for forms
- Utilize existing SortableTable component for data display

### Data Fetching and State Management
- WebSocket connections managed through WebSocketContext for real-time personal trading data
- Personal authentication state persisted in AuthContext
- All trading operations automatically generate activity log entries
- Chart data flows from WebSocketContext to TradingChart component

### Development Workflow
- Mock data is provided through WebSocketContext for development and testing
- Real-time price simulation runs automatically when WebSocket connects
- **Login credentials for demo**: username `trader01`, password `password123`
- All trading operations (create, modify, close, cancel) are fully functional with mock data
- Charts display real-time data generated from live quotes

### Application Routing Structure
```
/ (root)     → Direct access to trading terminal (simplified from broker landing)
```

**Current Flow**: Direct access to personal trading terminal with authentication

### Styling Conventions
- Tailwind CSS with custom color scheme optimized for trading terminal
- Dark theme optimized for professional trading environments
- Responsive design with mobile-first approach
- Professional "NTT" (Nexus Trader Terminal) branding

## Configuration Files

- **TypeScript**: Multi-project setup with `tsconfig.json` (base), `tsconfig.app.json`, `tsconfig.node.json`
  - Path aliases (`@/*` → `./src/*`) configured for clean imports
  - Relaxed strict mode settings for development flexibility
- **ESLint**: Flat config (`eslint.config.js`) with React Hooks and TypeScript integration
  - React Refresh plugin for hot reloading in development
  - Unused variables disabled for development workflow
- **Vite**: React SWC plugin for fast compilation, dev server auto-port detection
  - Lovable.dev component tagger integration for development mode only
  - Path alias resolution matches TypeScript configuration
- **Tailwind CSS**: Custom design system with CSS variables and dark mode support
- **PostCSS**: Autoprefixer for cross-browser compatibility

## Environment Setup

1. **Install dependencies**: `npm install` (or `bun install`)
2. **Configure reCAPTCHA**: Create `.env` file with development key:
   ```bash
   VITE_RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
   ```
   *(Google's test key for development)*
3. **Start development**: `npm run dev`
4. **Access application**: http://localhost:PORT (Vite auto-detects available port)
5. **Login credentials**: username `trader01`, password `password123`

## Important Implementation Notes

### Single Account Architecture
- All components designed for single personal trader account
- No multi-account complexity or dealer administrative features
- Personal P&L tracking and individual account management
- Simplified authentication without broker context

### Chart Integration
- KLineChart v10.0.0-alpha5 integrated for professional trading charts
- Real-time chart data generated from WebSocketContext live quotes
- Technical indicators: MA, EMA, RSI, MACD with multiple timeframes
- Charts accessible via context menus and modal display

### Data Flow
- WebSocketContext serves as single source of truth for all trading data
- Real-time price updates automatically refresh charts and P&L calculations
- Activity logging for personal trading operations
- Mock data simulation for development and testing

### Development Notes
- The codebase uses both npm and Bun lockfiles for package management flexibility
- Lovable.dev integration provides collaborative development features
- Hot reload enabled for efficient development workflow
- Component tagging system for development tracking