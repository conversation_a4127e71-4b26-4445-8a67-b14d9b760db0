import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { positions } from '../data/mockData';

export const PositionsPanel: React.FC = () => {
  return (
    <div className="flex-1 panel-bg panel-border">
      <div className="p-3 border-b border-trading-border">
        <h3 className="text-sm font-medium text-trading-text">Positions</h3>
      </div>
      <div className="overflow-y-auto">
        <table className="w-full">
          <thead className="bg-trading-panel border-b border-trading-border">
            <tr className="text-xs text-trading-text-secondary">
              <th className="py-2 px-2 text-left font-medium">Sd</th>
              <th className="py-2 px-2 text-left font-medium">Symbol</th>
              <th className="py-2 px-2 text-right font-medium">Qnty</th>
              <th className="py-2 px-2 text-right font-medium">P/L</th>
            </tr>
          </thead>
          <tbody>
            {positions.slice(0, 8).map((position, index) => (
              <tr key={index} className="text-xs border-b border-trading-border/50 hover:bg-trading-border/30">
                <td className="py-1.5 px-2">
                  {position.side === 'buy' ? (
                    <TrendingUp className="h-3 w-3 text-bull-green" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-bear-red" />
                  )}
                </td>
                <td className="py-1.5 px-2 text-trading-text font-medium">{position.symbol}</td>
                <td className="py-1.5 px-2 text-right text-trading-text">{position.quantity}</td>
                <td className={`py-1.5 px-2 text-right font-medium ${
                  position.profitLoss >= 0 ? 'text-bull-green' : 'text-bear-red'
                }`}>
                  {position.profitLoss >= 0 ? '+' : ''}{position.profitLoss.toFixed(2)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};