import React, { useState } from 'react';
import { useTradingContext } from '../contexts/TradingContext';

export const OrderTicket: React.FC = () => {
  const { selectedSymbol, getCurrentPair } = useTradingContext();
  const [volume, setVolume] = useState('0.01');
  const [orderType, setOrderType] = useState<'market' | 'limit' | 'stop'>('market');
  const [limitPrice, setLimitPrice] = useState('');
  const [stopLoss, setStopLoss] = useState('');
  const [takeProfit, setTakeProfit] = useState('');

  const currentPair = getCurrentPair();

  const handleBuy = () => {
    console.log('Buy order:', { symbol: selectedSymbol, volume, type: orderType });
  };

  const handleSell = () => {
    console.log('Sell order:', { symbol: selectedSymbol, volume, type: orderType });
  };

  return (
    <div className="h-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-3 overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="mb-3">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Order Ticket</h3>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {selectedSymbol} - {currentPair?.name}
          </div>
        </div>

        {/* Current Prices */}
        {currentPair && (
          <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <div className="flex justify-between text-xs">
              <div>
                <div className="text-gray-500 dark:text-gray-400">Bid</div>
                <div className="font-medium text-red-600">{currentPair.bid.toFixed(5)}</div>
              </div>
              <div className="text-right">
                <div className="text-gray-500 dark:text-gray-400">Ask</div>
                <div className="font-medium text-green-600">{currentPair.ask.toFixed(5)}</div>
              </div>
            </div>
          </div>
        )}

        {/* Order Form */}
        <div className="flex-1 space-y-3">
          {/* Volume */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Volume
            </label>
            <input
              type="number"
              value={volume}
              onChange={(e) => setVolume(e.target.value)}
              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
              step="0.01"
              min="0.01"
            />
          </div>

          {/* Order Type */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Type
            </label>
            <select
              value={orderType}
              onChange={(e) => setOrderType(e.target.value as 'market' | 'limit' | 'stop')}
              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
            >
              <option value="market">Market</option>
              <option value="limit">Limit</option>
              <option value="stop">Stop</option>
            </select>
          </div>

          {/* Limit Price (only for limit/stop orders) */}
          {orderType !== 'market' && (
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Price
              </label>
              <input
                type="number"
                value={limitPrice}
                onChange={(e) => setLimitPrice(e.target.value)}
                className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                step="0.00001"
              />
            </div>
          )}

          {/* Stop Loss */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stop Loss
            </label>
            <input
              type="number"
              value={stopLoss}
              onChange={(e) => setStopLoss(e.target.value)}
              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
              step="0.00001"
              placeholder="Optional"
            />
          </div>

          {/* Take Profit */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Take Profit
            </label>
            <input
              type="number"
              value={takeProfit}
              onChange={(e) => setTakeProfit(e.target.value)}
              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
              step="0.00001"
              placeholder="Optional"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 mt-4">
          <button
            onClick={handleSell}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
          >
            SELL
          </button>
          <button
            onClick={handleBuy}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
          >
            BUY
          </button>
        </div>
      </div>
    </div>
  );
};