import React from 'react';
import { positions } from '../data/mockData';
import { TrendingUp, TrendingDown } from 'lucide-react';

export const PositionsTab: React.FC = () => {
  return (
    <div className=\"h-full overflow-y-auto p-4\">
      <table className=\"w-full text-xs\">
        <thead>
          <tr className=\"text-trading-text-secondary border-b border-trading-border/50\">
            <th className=\"px-2 py-2 text-left font-medium\">Sd</th>
            <th className=\"px-2 py-2 text-left font-medium\">Symbol</th>
            <th className=\"px-2 py-2 text-left font-medium\">Qnty</th>
            <th className=\"px-2 py-2 text-left font-medium\">P/L</th>
            <th className=\"px-2 py-2 text-left font-medium\">Prtcn</th>
          </tr>
        </thead>
        <tbody>
          {positions.map((position, index) => (
            <tr key={index} className=\"hover:bg-trading-border/20 border-b border-trading-border/30\">
              <td className=\"px-2 py-2\">
                {position.side === 'buy' ? (
                  <TrendingUp className=\"h-4 w-4 text-bull\" />
                ) : (
                  <TrendingDown className=\"h-4 w-4 text-bear\" />
                )}
              </td>
              <td className=\"px-2 py-2 text-trading-text font-medium\">{position.symbol}</td>
              <td className=\"px-2 py-2 text-trading-text\">{position.quantity}</td>
              <td className={`px-2 py-2 font-medium ${
                position.profitLoss >= 0 ? 'text-bull' : 'text-bear'
              }`}>
                {position.profitLoss >= 0 ? '+' : ''}{position.profitLoss.toFixed(2)}
              </td>
              <td className=\"px-2 py-2 text-trading-text-secondary\">—</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};