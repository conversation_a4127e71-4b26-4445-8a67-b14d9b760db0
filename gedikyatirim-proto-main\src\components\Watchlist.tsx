import React from 'react';
import { useTradingContext } from '../contexts/TradingContext';
import { currencyPairs } from '../data/mockData';

export const Watchlist: React.FC = () => {
  const { selectedSymbol, setSelectedSymbol } = useTradingContext();

  const eurPairs = currencyPairs.filter(pair => pair.symbol.startsWith('EUR/'));
  const usdPairs = currencyPairs.filter(pair => pair.symbol.startsWith('USD/'));

  const renderPairRow = (pair: any) => (
    <tr
      key={pair.symbol}
      onClick={() => setSelectedSymbol(pair.symbol)}
      className={`cursor-pointer text-xs border-b border-trading-border/50 hover:bg-trading-border/30 transition-colors ${
        selectedSymbol === pair.symbol ? 'bg-bull-green-bg' : ''
      }`}
    >
      <td className="py-1.5 px-2 font-medium text-trading-text">{pair.symbol}</td>
      <td className="py-1.5 px-1 text-right text-trading-text">{pair.bid.toFixed(5)}</td>
      <td className="py-1.5 px-1 text-right text-trading-text">{pair.ask.toFixed(5)}</td>
      <td className={`py-1.5 px-2 text-right font-medium ${
        pair.changePercent >= 0 ? 'text-bull-green' : 'text-bear-red'
      }`}>
        {pair.changePercent.toFixed(3)}
      </td>
    </tr>
  );

  return (
    <div className="flex-1 panel-bg panel-border border-b overflow-hidden">
      <div className="h-full overflow-y-auto">
        {/* EUR Pairs Section */}
        <div>
          <table className="w-full">
            <thead className="sticky top-0 bg-trading-panel">
              <tr className="text-xs text-trading-text-secondary border-b border-trading-border">
                <th className="py-2 px-2 text-left font-medium">Symbol</th>
                <th className="py-2 px-1 text-right font-medium">Bid</th>
                <th className="py-2 px-1 text-right font-medium">Ask</th>
                <th className="py-2 px-2 text-right font-medium">Chng %</th>
              </tr>
            </thead>
            <tbody>
              {eurPairs.map(renderPairRow)}
            </tbody>
          </table>
        </div>

        {/* USD Pairs Section */}
        <div className="mt-4">
          <table className="w-full">
            <thead className="sticky top-0 bg-trading-panel">
              <tr className="text-xs text-trading-text-secondary border-b border-trading-border">
                <th className="py-2 px-2 text-left font-medium">Symbol</th>
                <th className="py-2 px-1 text-right font-medium">Bid</th>
                <th className="py-2 px-1 text-right font-medium">Ask</th>
                <th className="py-2 px-2 text-right font-medium">Chng %</th>
              </tr>
            </thead>
            <tbody>
              {usdPairs.map(renderPairRow)}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};