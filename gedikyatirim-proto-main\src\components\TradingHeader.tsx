import React from 'react';
import { ChevronDown, RotateCcw, Save, Maximize } from 'lucide-react';
import { accountSummary } from '../data/mockData';

export const TradingHeader: React.FC = () => {
  return (
    <header className="h-14 panel-bg panel-border border-b flex items-center justify-between px-4">
      {/* Left Section */}
      <div className="flex items-center space-x-6">
        <h1 className="text-xl font-semibold text-trading-text">Gedikyatırım</h1>
        <div className="flex items-center space-x-1 text-sm">
          <span className="text-trading-text-secondary">Regular trading</span>
          <ChevronDown className="h-4 w-4 text-trading-text-secondary" />
        </div>
      </div>

      {/* Center Navigation */}
      <nav className="flex items-center space-x-1">
        {['Trade', 'Market', 'News', 'Ideas', 'Account'].map((item, index) => (
          <button
            key={item}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              index === 0
                ? 'bg-accent text-accent-foreground'
                : 'text-trading-text-secondary hover:text-trading-text hover:bg-trading-border/50'
            }`}
          >
            {item}
          </button>
        ))}
        <button className="px-3 py-2 text-sm font-medium text-trading-text-secondary hover:text-trading-text">
          +
        </button>
      </nav>

      {/* Right Section - Account Summary */}
      <div className="flex items-center space-x-6">
        <div className="flex items-center space-x-4 text-xs">
          <div className="text-center">
            <div className="text-trading-text-secondary">Balance</div>
            <div className="font-medium text-trading-text">{accountSummary.balance.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-secondary">Equity</div>
            <div className="font-medium text-trading-text">{accountSummary.equity.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-secondary">FPL</div>
            <div className="font-medium text-bull">{accountSummary.fpl.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-secondary">Used Margin</div>
            <div className="font-medium text-trading-text">{accountSummary.usedMargin.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-secondary">Usable Margin</div>
            <div className="font-medium text-trading-text">{accountSummary.usableMargin.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-secondary">Account</div>
            <div className="font-medium text-trading-text">{accountSummary.account}, {accountSummary.currency}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-secondary">User</div>
            <div className="font-medium text-trading-text">{accountSummary.user}</div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="p-1.5 text-trading-text-secondary hover:text-trading-text">
            <RotateCcw className="h-4 w-4" />
          </button>
          <button className="p-1.5 text-trading-text-secondary hover:text-trading-text">
            <Save className="h-4 w-4" />
          </button>
          <button className="p-1.5 text-trading-text-secondary hover:text-trading-text">
            <Maximize className="h-4 w-4" />
          </button>
        </div>
      </div>
    </header>
  );
};