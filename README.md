# Nexus Trader Terminal

A professional personal trading terminal built as a React single-page application. Provides real-time trading capabilities, position management, advanced charting with technical analysis, and secure authentication for individual traders.

![Nexus Trader Terminal](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![React](https://img.shields.io/badge/React-18.3.1-blue)
![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue)
![Vite](https://img.shields.io/badge/Vite-5.4.1-purple)

## 🚀 Features

### Core Trading Functionality
- **Personal Account Management**: Single trader account with real-time balance and equity tracking
- **Position Management**: Open positions with live P&L tracking and risk management
- **Order Management**: Pending orders with modification and cancellation capabilities
- **Live Price Quotes**: Real-time market data with advanced charting integration
- **Personal Activity Log**: Individual trading activity tracking and history

### Advanced Charting
- **Professional Charts**: KLineChart integration with technical indicators
- **Technical Analysis**: Moving Averages, RSI, MACD, Volume indicators
- **Multiple Timeframes**: 1-minute to daily charts with real-time data updates
- **Chart-Focused Layout**: Professional trading interface similar to Binance/TradingView

### Trading Panel Features
- **Multiple Trading Modes**: Spot, Cross, Isolated, Grid trading
- **Order Types**: Market, Limit, Stop Limit orders
- **Risk Management**: Take Profit and Stop Loss controls
- **Real-time Integration**: Live data synchronization

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18.3.1 with TypeScript and Vite 5.4.1
- **UI Components**: shadcn/ui component library (40+ pre-built components)
- **Charts**: KLineChart v10.0.0-alpha5 for professional trading charts
- **State Management**: TanStack React Query v5 + React Contexts
- **Routing**: React Router DOM v6
- **Styling**: Tailwind CSS v3 with custom design system
- **Forms**: React Hook Form + Zod validation
- **Security**: Google reCAPTCHA v2 integration

### Layout Architecture
```
┌─────────────────────────────────────────────────────────────┐
│ Header (Nexus Trader Terminal)                              │
├─────────────────────────────────────────────────────────────┤
│ Left Sidebar    │ Main Chart Area    │ Right Sidebar        │
│ (20% width)     │ (55% width)        │ (25% width)          │
│                 │                    │                      │
│ • Price Quotes  │ • TradingChart     │ • Advanced Trading   │
│ • Quick Stats   │   (primary focus)  │   Panel (Spot/Grid)  │
│ • Symbol Click  │ • Real-time Data   │ • Buy/Sell Controls  │
│                 │ • Technical        │ • TP/SL Management   │
│                 │   Indicators       │                      │
│                 │                    │ • Account Info       │
│                 │                    │ • Balance/Equity     │
├─────────────────┴────────────────────┴──────────────────────┤
│ Bottom Panel (spans Left + Chart only)                      │
│ • Position/Order tables • Activity Log • Data filtering     │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ or Bun
- npm or bun package manager

### Installation
```bash
# Clone the repository
git clone https://github.com/[username]/nexus-trader-terminal.git
cd nexus-trader-terminal

# Install dependencies
npm install
# or
bun install

# Configure environment
cp .env.example .env
# Add your reCAPTCHA site key to .env file
```

### Development
```bash
# Start development server (auto-detects available port, typically 8080+)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

### Demo Access
- **URL**: http://localhost:[PORT] (Vite auto-detects available port)
- **Username**: `trader01`
- **Password**: `password123`
- **reCAPTCHA**: Development key included for testing

## 📊 Trading Features

### Personal Account Dashboard
- Real-time balance and equity display
- Margin level monitoring with visual indicators
- Personal P&L tracking with profit/loss percentages
- Account status monitoring (Healthy/Warning/Margin Call)

### Advanced Trading Panel
- **Order Modes**: Spot, Cross, Isolated, Grid trading
- **Order Types**: Market, Limit, Stop Limit with proper validation
- **Price Controls**: Real-time price input with currency selectors
- **Volume Management**: Amount input with percentage-based sliders
- **Risk Management**: Take Profit and Stop Loss with limit/offset settings

### Professional Charting
- **Real-time Charts**: Live price updates with chart synchronization
- **Technical Indicators**: MA, EMA, RSI, MACD with configurable parameters
- **Timeframe Support**: 1m, 5m, 15m, 30m, 1h, 4h, 1d with data aggregation
- **Modal Display**: Full-screen chart modals accessible from price quotes

### Position & Order Management
- **Open Positions**: Real-time P&L tracking with modify/close capabilities
- **Pending Orders**: Order modification and cancellation
- **Context Menus**: Right-click actions for quick operations
- **Sortable Tables**: Professional data display with filtering

## 🔧 Configuration

### Environment Variables
```bash
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here
```

### Development Configuration
- **TypeScript**: Multi-project setup with path aliases (`@/*` → `./src/*`)
- **ESLint**: Flat config with React Hooks and TypeScript integration
- **Vite**: React SWC plugin for fast compilation
- **Tailwind**: Custom design system with dark theme optimization

## 🛠️ Development

### Project Structure
```
src/
├── components/
│   ├── ui/                    # shadcn/ui components
│   ├── modals/                # Trading modals
│   ├── tabs/                  # Data view tabs
│   ├── Dashboard.tsx          # Main trading interface
│   ├── TradingChart.tsx       # Professional chart component
│   ├── TradePanel.tsx         # Advanced trading panel
│   └── ...
├── contexts/                  # React contexts
├── types/                     # TypeScript definitions
├── utils/                     # Utility functions
└── hooks/                     # Custom React hooks
```

### Key Components
- **Dashboard**: Main trading interface with resizable panels
- **TradingChart**: KLineChart integration with technical indicators
- **TradePanel**: Advanced trading interface with Binance-like features
- **PersonalStatsPanel**: Individual trader statistics
- **AccountInfoPanel**: Single account information display

### Development Patterns
- Single-account architecture (no multi-account complexity)
- Real-time WebSocket data management
- Professional trading platform UX patterns
- Component-based modal workflows
- Responsive design with mobile-first approach

## 🧪 Testing

```bash
# Run tests (when configured)
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📦 Building & Deployment

```bash
# Production build
npm run build

# Analyze bundle
npm run build:analyze

# Preview production build locally
npm run preview
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **KLineChart**: Professional trading charts
- **shadcn/ui**: Beautiful and accessible UI components
- **TanStack Query**: Powerful data synchronization
- **Tailwind CSS**: Utility-first CSS framework

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**Nexus Trader Terminal** - Professional trading platform for individual traders.
