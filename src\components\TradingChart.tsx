import React, { useEffect, useRef, useState } from 'react';
import { init, dispose, Chart } from 'klinecharts';
import { useWebSocket } from '../contexts/WebSocketContext';
import { KLineData, TimeframeOption } from '../types/trading';
import { X, Settings, TrendingUp, BarChart3 } from 'lucide-react';

interface TradingChartProps {
  symbol: string;
  onClose?: () => void;
  className?: string;
  inline?: boolean; // New prop for inline display
}

const TradingChart: React.FC<TradingChartProps> = ({ symbol, onClose, className = '', inline = false }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  const { chartData, quotes } = useWebSocket();
  
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1m');
  const [showSettings, setShowSettings] = useState(false);
  const [chartConfig, setChartConfig] = useState({
    candleType: 'candle_solid',
    showVolume: true,
    showGrid: true,
    showCrosshair: true,
    indicators: {
      ma: false,
      ema: false,
      rsi: false,
      macd: false
    }
  });

  const timeframes: TimeframeOption[] = [
    { multiplier: 1, timespan: 'minute', text: '1m' },
    { multiplier: 5, timespan: 'minute', text: '5m' },
    { multiplier: 15, timespan: 'minute', text: '15m' },
    { multiplier: 30, timespan: 'minute', text: '30m' },
    { multiplier: 1, timespan: 'hour', text: '1h' },
    { multiplier: 4, timespan: 'hour', text: '4h' },
    { multiplier: 1, timespan: 'day', text: '1d' }
  ];

  // Initialize chart
  useEffect(() => {
    if (!chartRef.current) return;

    try {
      const chart = init(chartRef.current);
      chartInstanceRef.current = chart;

      // Configure chart styles
      chart.setStyles({
        candle: {
          type: chartConfig.candleType as any,
          bar: {
            upColor: '#26a69a',
            downColor: '#ef5350',
            noChangeColor: '#888888'
          },
          tooltip: {
            showRule: 'always',
            showType: 'standard',
            custom: null,
            defaultValue: 'n/a',
            rectPosition: 'fixed',
            rectPaddingLeft: 0,
            rectPaddingRight: 0,
            rectPaddingTop: 0,
            rectPaddingBottom: 6,
            rectOffsetLeft: 8,
            rectOffsetTop: 8,
            rectOffsetRight: 8,
            rectOffsetBottom: 8,
            rectBorderSize: 1,
            rectBorderRadius: 4,
            rectBorderColor: '#3f4254',
            rectBackgroundColor: 'rgba(17, 17, 17, .3)',
            textSize: 12,
            textFamily: 'Helvetica Neue',
            textWeight: 'normal',
            textColor: '#d9d9d9',
            textMarginLeft: 8,
            textMarginTop: 6,
            textMarginRight: 8,
            textMarginBottom: 6
          }
        },
        grid: {
          show: chartConfig.showGrid,
          horizontal: {
            show: chartConfig.showGrid,
            size: 1,
            color: '#393939',
            style: 'dashed'
          },
          vertical: {
            show: chartConfig.showGrid,
            size: 1,
            color: '#393939',
            style: 'dashed'
          }
        },
        crosshair: {
          show: chartConfig.showCrosshair,
          horizontal: {
            show: chartConfig.showCrosshair,
            line: {
              show: true,
              style: 'dashed',
              dashValue: [4, 2],
              size: 1,
              color: '#888888'
            },
            text: {
              show: true,
              color: '#D9D9D9',
              size: 12,
              family: 'Helvetica Neue',
              weight: 'normal',
              borderStyle: 'solid',
              borderSize: 1,
              borderColor: '#505050',
              borderRadius: 2,
              paddingLeft: 4,
              paddingRight: 4,
              paddingTop: 2,
              paddingBottom: 2,
              backgroundColor: '#505050'
            }
          },
          vertical: {
            show: chartConfig.showCrosshair,
            line: {
              show: true,
              style: 'dashed',
              dashValue: [4, 2],
              size: 1,
              color: '#888888'
            },
            text: {
              show: true,
              color: '#D9D9D9',
              size: 12,
              family: 'Helvetica Neue',
              weight: 'normal',
              borderStyle: 'solid',
              borderSize: 1,
              borderColor: '#505050',
              borderRadius: 2,
              paddingLeft: 4,
              paddingRight: 4,
              paddingTop: 2,
              paddingBottom: 2,
              backgroundColor: '#505050'
            }
          }
        }
      });

      // Load initial data
      if (chartData && chartData.length > 0) {
        chart.applyNewData(chartData);
      }

      // Add volume indicator if enabled
      if (chartConfig.showVolume) {
        chart.createIndicator('VOL', false);
      }

      // Add technical indicators based on config
      if (chartConfig.indicators.ma) {
        chart.createIndicator('MA', true);
      }
      if (chartConfig.indicators.ema) {
        chart.createIndicator('EMA', true);
      }
      if (chartConfig.indicators.rsi) {
        chart.createIndicator('RSI', false);
      }
      if (chartConfig.indicators.macd) {
        chart.createIndicator('MACD', false);
      }

    } catch (error) {
      console.error('Failed to initialize chart:', error);
    }

    return () => {
      if (chartInstanceRef.current) {
        dispose(chartRef.current);
        chartInstanceRef.current = null;
      }
    };
  }, [chartConfig]);

  // Update chart data when new data arrives
  useEffect(() => {
    if (chartInstanceRef.current && chartData && chartData.length > 0) {
      // Filter data for the selected symbol
      const symbolData = chartData.filter(data => data.timestamp);
      if (symbolData.length > 0) {
        chartInstanceRef.current.applyNewData(symbolData);
      }
    }
  }, [chartData, symbol]);

  // Real-time price updates
  useEffect(() => {
    const currentQuote = quotes.find(q => q.symbol === symbol);
    if (chartInstanceRef.current && currentQuote) {
      // Create a new candlestick with current price
      const now = Date.now();
      const newCandle: KLineData = {
        timestamp: now,
        open: currentQuote.bid,
        high: Math.max(currentQuote.bid, currentQuote.ask),
        low: Math.min(currentQuote.bid, currentQuote.ask),
        close: currentQuote.ask,
        volume: Math.floor(Math.random() * 1000) + 500 // Mock volume
      };

      // Update the last candle or add new one
      chartInstanceRef.current.updateData(newCandle);
    }
  }, [quotes, symbol]);

  const handleTimeframeChange = (timeframe: string) => {
    setSelectedTimeframe(timeframe);
    // In a real implementation, you would fetch new data for the selected timeframe
    console.log('Timeframe changed to:', timeframe);
  };

  const handleIndicatorToggle = (indicator: keyof typeof chartConfig.indicators) => {
    setChartConfig(prev => ({
      ...prev,
      indicators: {
        ...prev.indicators,
        [indicator]: !prev.indicators[indicator]
      }
    }));
  };

  const currentPrice = quotes.find(q => q.symbol === symbol);

  return (
    <div className={`bg-slate-800 rounded-lg border border-slate-700 ${className}`}>
      {/* Chart Header */}
      <div className="flex items-center justify-between p-3 border-b border-slate-700">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-blue-400" />
            <h3 className={`${inline ? 'text-lg' : 'text-lg'} font-semibold text-white`}>{symbol}</h3>
            {currentPrice && (
              <div className="flex items-center space-x-2 ml-4">
                <span className="text-sm text-gray-400">Current:</span>
                <span className={`font-mono font-semibold ${
                  currentPrice.dailyChange >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {currentPrice.ask.toFixed(5)}
                </span>
                <span className={`text-xs ${
                  currentPrice.dailyChange >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {currentPrice.dailyChange >= 0 ? '+' : ''}{(currentPrice.dailyChange * 100).toFixed(2)}%
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Timeframe Selector */}
          <div className="flex items-center space-x-1 bg-slate-700 rounded-lg p-1">
            {timeframes.map((tf) => (
              <button
                key={tf.text}
                onClick={() => handleTimeframeChange(tf.text)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  selectedTimeframe === tf.text
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-slate-600'
                }`}
              >
                {tf.text}
              </button>
            ))}
          </div>

          {/* Settings Button */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
            title="Chart Settings"
          >
            <Settings className="w-4 h-4" />
          </button>

          {/* Close Button - Only show if not inline */}
          {onClose && !inline && (
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded transition-colors"
              title="Close Chart"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="p-3 border-b border-slate-700 bg-slate-700/30">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <h4 className="text-sm font-medium text-white mb-2">Chart Type</h4>
              <select
                value={chartConfig.candleType}
                onChange={(e) => setChartConfig(prev => ({ ...prev, candleType: e.target.value }))}
                className="w-full px-2 py-1 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="candle_solid">Solid Candles</option>
                <option value="candle_stroke">Hollow Candles</option>
                <option value="candle_up_stroke">Up Stroke</option>
                <option value="candle_down_stroke">Down Stroke</option>
                <option value="ohlc">OHLC Bars</option>
                <option value="area">Area</option>
              </select>
            </div>

            <div>
              <h4 className="text-sm font-medium text-white mb-2">Display</h4>
              <div className="space-y-1">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.showVolume}
                    onChange={(e) => setChartConfig(prev => ({ ...prev, showVolume: e.target.checked }))}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">Volume</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.showGrid}
                    onChange={(e) => setChartConfig(prev => ({ ...prev, showGrid: e.target.checked }))}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">Grid</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.showCrosshair}
                    onChange={(e) => setChartConfig(prev => ({ ...prev, showCrosshair: e.target.checked }))}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">Crosshair</span>
                </label>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-white mb-2">Moving Averages</h4>
              <div className="space-y-1">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.indicators.ma}
                    onChange={() => handleIndicatorToggle('ma')}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">MA</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.indicators.ema}
                    onChange={() => handleIndicatorToggle('ema')}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">EMA</span>
                </label>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-white mb-2">Oscillators</h4>
              <div className="space-y-1">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.indicators.rsi}
                    onChange={() => handleIndicatorToggle('rsi')}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">RSI</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={chartConfig.indicators.macd}
                    onChange={() => handleIndicatorToggle('macd')}
                    className="rounded bg-slate-600 border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                  />
                  <span className="text-xs text-gray-300">MACD</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chart Container */}
      <div className={`${inline ? 'p-3' : 'p-3'}`}>
        <div
          ref={chartRef}
          className={`w-full bg-slate-900 rounded border border-slate-600 ${inline ? 'h-full' : ''}`}
          style={{ height: inline ? 'calc(100% - 60px)' : '400px' }}
        />
      </div>
    </div>
  );
};

export default TradingChart;