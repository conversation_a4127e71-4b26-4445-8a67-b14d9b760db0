import React, { useMemo } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import { User, Activity, DollarSign, TrendingUp, Target, Clock } from 'lucide-react';

interface PersonalStatsPanelProps {
  collapsed?: boolean;
}

const PersonalStatsPanel: React.FC<PersonalStatsPanelProps> = ({ collapsed = false }) => {
  const { traderAccount, positions, orders } = useWebSocket();

  const analytics = useMemo(() => {
    // Today's activities (positions + orders)
    const todayActivities = positions.length + orders.length;

    // P/L calculations
    const totalPL = positions.reduce((sum, position) => sum + position.pl, 0);
    const profitablePositions = positions.filter(pos => pos.pl > 0).length;
    const profitPercentage = positions.length > 0 ? Math.round((profitablePositions / positions.length) * 100) : 0;

    // Risk metrics
    const marginUtilization = traderAccount.usedMargin > 0 ? 
      Math.round((traderAccount.usedMargin / traderAccount.equity) * 100) : 0;

    return {
      todayActivities,
      totalPL,
      profitPercentage,
      marginUtilization,
      totalPositions: positions.length,
      totalOrders: orders.length
    };
  }, [traderAccount, positions, orders]);

  const formatPL = (value: number) => {
    const absValue = Math.abs(value);
    if (absValue >= 1000) {
      return `${value >= 0 ? '+' : '-'}$${(absValue / 1000).toFixed(1)}K`;
    }
    return `${value >= 0 ? '+' : ''}$${value.toFixed(0)}`;
  };

  const getMarginUtilizationColor = (utilization: number) => {
    if (utilization > 80) return 'text-red-400';
    if (utilization > 60) return 'text-orange-400';
    return 'text-green-400';
  };

  return (
    <div className="bg-slate-800 rounded-lg p-2 sm:p-4 border border-slate-700 w-full">
      {/* Header */}
      <div className="flex items-center space-x-2 mb-2 sm:mb-4">
        <User className="w-4 sm:w-5 h-4 sm:h-5 text-blue-400" />
        <h2 className="text-base sm:text-lg font-semibold text-white">Personal Stats</h2>
      </div>
      
      {/* Account Summary */}
      <div className={`${collapsed ? 'mb-2' : 'mb-2 sm:mb-4'} flex-shrink-0`}>
        <div className="flex items-center space-x-3 mb-1">
          <DollarSign className="w-4 h-4 text-green-400" />
          <span className={`${collapsed ? 'text-xs' : 'text-sm'} font-medium text-white`}>Balance: ${traderAccount.balance?.toFixed(2) || '0.00'}</span>
        </div>
        {!collapsed && (
          <div className="ml-7">
            <span className="text-sm text-gray-400">Equity: </span>
            <span className="text-white font-semibold">${traderAccount.equity?.toFixed(2) || '0.00'}</span>
          </div>
        )}
      </div>

      {/* Section Divider */}
      {!collapsed && <div className="border-t border-slate-700 mb-2 sm:mb-4"></div>}

      {/* Performance Metrics */}
      <div className={`${collapsed ? 'space-y-2' : 'space-y-3'} flex-1 min-h-0 overflow-y-auto`}>
        {/* Trading Activity */}
        <div className="flex items-center space-x-3">
          <Activity className="w-4 h-4 text-yellow-400" />
          <span className={`${collapsed ? 'text-xs' : 'text-sm'} text-gray-400`}>Active:</span>
          <div className="flex items-center space-x-2">
            <span className={`${collapsed ? 'text-xs' : 'text-sm'} text-white font-semibold`}>{analytics.totalPositions} pos</span>
            {!collapsed && (
              <>
                <span className="text-gray-500">•</span>
                <span className="text-white font-semibold">{analytics.totalOrders} orders</span>
              </>
            )}
          </div>
        </div>

        {/* P/L Summary */}
        <div className="flex items-center space-x-3">
          <TrendingUp className={`w-4 h-4 ${analytics.totalPL >= 0 ? 'text-green-400' : 'text-red-400'}`} />
          <span className={`${collapsed ? 'text-xs' : 'text-sm'} text-gray-400`}>P/L:</span>
          <div className="flex items-center space-x-2">
            <span className={`${collapsed ? 'text-xs' : 'text-sm'} font-semibold ${analytics.totalPL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPL(analytics.totalPL)}
            </span>
            {!collapsed && (
              <span className="text-xs text-gray-500">
                ({analytics.profitPercentage}% {analytics.profitPercentage >= 50 ? '↗' : '↘'})
              </span>
            )}
          </div>
        </div>

        {/* Margin Utilization */}
        <div className="flex items-center space-x-3">
          <Target className={`w-4 h-4 ${getMarginUtilizationColor(analytics.marginUtilization)}`} />
          <span className="text-sm text-gray-400">Margin:</span>
          <div className="flex items-center space-x-2">
            <span className={`font-semibold ${getMarginUtilizationColor(analytics.marginUtilization)}`}>
              {analytics.marginUtilization}%
            </span>
            <span className="text-xs text-gray-500">
              ${traderAccount.usedMargin?.toFixed(2) || '0.00'} used
            </span>
          </div>
        </div>
      </div>

      {/* Footer: Last Updated */}
      <div className="mt-2 sm:mt-4 pt-2 sm:pt-3 border-t border-slate-700">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Updated:</span>
          <span className="text-xs text-gray-400">{new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  );
};

export default PersonalStatsPanel;