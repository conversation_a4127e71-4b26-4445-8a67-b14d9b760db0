import React, { useState } from 'react';
import { OrdersTab } from './OrdersTab';
import { positions } from '../data/mockData';
import { TrendingUp, TrendingDown } from 'lucide-react';

export const BottomPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Orders');
  const tabs = ['Orders', 'Positions', 'Alerts', 'Chart'];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Orders':
        return <OrdersTab />;
      case 'Positions':
        return (
          <div className="h-full overflow-y-auto">
            <table className="w-full">
              <thead className="sticky top-0 bg-trading-panel border-b border-trading-border">
                <tr className="text-xs text-trading-text-secondary">
                  <th className="py-3 px-3 text-left font-medium">Sd</th>
                  <th className="py-3 px-3 text-left font-medium">Symbol</th>
                  <th className="py-3 px-3 text-right font-medium">Qnty</th>
                  <th className="py-3 px-3 text-right font-medium">P/L</th>
                  <th className="py-3 px-3 text-center font-medium">Prtcn</th>
                </tr>
              </thead>
              <tbody>
                {positions.map((position, index) => (
                  <tr key={index} className="text-xs border-b border-trading-border/50 hover:bg-trading-border/30">
                    <td className="py-2 px-3">
                      {position.side === 'buy' ? (
                        <TrendingUp className="h-4 w-4 text-bull-green" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-bear-red" />
                      )}
                    </td>
                    <td className="py-2 px-3 text-trading-text font-medium">{position.symbol}</td>
                    <td className="py-2 px-3 text-right text-trading-text">{position.quantity}</td>
                    <td className={`py-2 px-3 text-right font-medium ${
                      position.profitLoss >= 0 ? 'text-bull-green' : 'text-bear-red'
                    }`}>
                      {position.profitLoss >= 0 ? '+' : ''}{position.profitLoss.toFixed(2)}
                    </td>
                    <td className="py-2 px-3 text-center text-trading-text">SL TP</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      case 'Alerts':
        return (
          <div className="flex items-center justify-center h-full text-trading-text-secondary">
            <p>No alerts configured</p>
          </div>
        );
      case 'Chart':
        return (
          <div className="flex items-center justify-center h-full text-trading-text-secondary">
            <p>Chart settings panel would appear here</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="h-60 panel-bg panel-border border-t flex flex-col">
      {/* Tab Navigation */}
      <div className="flex items-center border-b border-trading-border">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab
                ? 'border-accent text-accent bg-accent/5'
                : 'border-transparent text-trading-text-secondary hover:text-trading-text hover:border-trading-border'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>
    </div>
  );
};