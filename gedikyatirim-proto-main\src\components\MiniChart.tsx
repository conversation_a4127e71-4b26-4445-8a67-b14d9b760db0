import React from 'react';
import { useTradingContext } from '../contexts/TradingContext';

export const MiniChart: React.FC = () => {
  const { getCurrentPair } = useTradingContext();
  const currentPair = getCurrentPair();

  if (!currentPair) return null;

  return (
    <div className="h-32 panel-bg panel-border border-b p-3">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-trading-text-secondary">
          1 min: day
        </span>
      </div>
      
      {/* Simple mock chart representation */}
      <div className="h-16 bg-chart-grid/30 rounded relative overflow-hidden">
        <svg width="100%" height="100%" className="absolute inset-0">
          {/* Bollinger Bands */}
          <path 
            d="M0,20 Q50,15 100,25 T200,20" 
            stroke="hsl(var(--bollinger-upper))" 
            strokeWidth="1" 
            fill="none"
            vectorEffect="non-scaling-stroke"
          />
          <path 
            d="M0,32 Q50,30 100,35 T200,32" 
            stroke="hsl(var(--bollinger-mid))" 
            strokeWidth="1" 
            fill="none"
            vectorEffect="non-scaling-stroke"
          />
          <path 
            d="M0,44 Q50,42 100,48 T200,44" 
            stroke="hsl(var(--bollinger-lower))" 
            strokeWidth="1" 
            fill="none"
            vectorEffect="non-scaling-stroke"
          />
          
          {/* Price line */}
          <path 
            d="M0,35 Q30,28 60,32 Q90,38 120,30 Q150,25 180,35 L200,32" 
            stroke="hsl(var(--chart-up))" 
            strokeWidth="2" 
            fill="none"
            vectorEffect="non-scaling-stroke"
          />
        </svg>
      </div>
      
      <div className="flex justify-between text-xs mt-1">
        <span className="text-bear-red font-medium">{currentPair.bid.toFixed(5)}</span>
        <span className="text-bull-green font-medium">{currentPair.ask.toFixed(5)}</span>
      </div>
    </div>
  );
};