import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				inter: ['Inter', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				
				/* Trading specific colors */
				'trading-bg': 'hsl(var(--trading-bg))',
				'trading-panel': 'hsl(var(--trading-panel))',
				'trading-border': 'hsl(var(--trading-border))',
				'trading-text': 'hsl(var(--trading-text))',
				'trading-text-secondary': 'hsl(var(--trading-text-secondary))',
				
				/* Market colors */
				'bull-green': 'hsl(var(--bull-green))',
				'bull-green-bg': 'hsl(var(--bull-green-bg))',
				'bear-red': 'hsl(var(--bear-red))',
				'bear-red-bg': 'hsl(var(--bear-red-bg))',
				
				/* Chart colors */
				'chart-grid': 'hsl(var(--chart-grid))',
				'chart-axis': 'hsl(var(--chart-axis))',
				'chart-up': 'hsl(var(--chart-up))',
				'chart-down': 'hsl(var(--chart-down))',
				'chart-volume': 'hsl(var(--chart-volume))',
				
				/* Indicators */
				'bollinger-upper': 'hsl(var(--bollinger-upper))',
				'bollinger-mid': 'hsl(var(--bollinger-mid))',
				'bollinger-lower': 'hsl(var(--bollinger-lower))',
				
				/* UI Elements */
				'button-buy': 'hsl(var(--button-buy))',
				'button-buy-hover': 'hsl(var(--button-buy-hover))',
				'button-sell': 'hsl(var(--button-sell))',
				'button-sell-hover': 'hsl(var(--button-sell-hover))',
				
				/* Status colors */
				'status-filled': 'hsl(var(--status-filled))',
				'status-pending': 'hsl(var(--status-pending))',
				'status-canceled': 'hsl(var(--status-canceled))',
				'status-rejected': 'hsl(var(--status-rejected))',
				'status-info': 'hsl(var(--status-info))',
				
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;