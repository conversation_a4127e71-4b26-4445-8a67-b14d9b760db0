import React from 'react';
import { orders } from '../data/mockData';
import { CheckCircle, XCircle, AlertCircle, Clock, Settings } from 'lucide-react';

export const OrdersTab: React.FC = () => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'filled':
        return <CheckCircle className="h-3 w-3 text-status-filled" />;
      case 'canceled':
        return <XCircle className="h-3 w-3 text-status-canceled" />;
      case 'rejected':
        return <AlertCircle className="h-3 w-3 text-status-rejected" />;
      case 'pending':
        return <Clock className="h-3 w-3 text-status-pending" />;
      case 'working':
        return <Settings className="h-3 w-3 text-status-info animate-spin" />;
      default:
        return <AlertCircle className="h-3 w-3 text-status-canceled" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'filled':
        return 'text-status-filled';
      case 'canceled':
        return 'text-status-canceled';
      case 'rejected':
        return 'text-status-rejected';
      case 'pending':
        return 'text-status-pending';
      case 'working':
        return 'text-status-info';
      default:
        return 'text-status-canceled';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Filter Controls */}
      <div className="p-3 border-b border-trading-border">
        <div className="flex items-center space-x-4 text-xs">
          <span className="text-trading-text-secondary">Filtered by:</span>
          <div className="flex items-center space-x-2">
            {['Canceled', 'Expired', 'Filled', 'Working'].map((filter) => (
              <label key={filter} className="flex items-center space-x-1 cursor-pointer">
                <input type="checkbox" className="rounded" defaultChecked />
                <span className="text-trading-text">{filter}</span>
              </label>
            ))}
          </div>
          <button className="px-2 py-1 text-accent hover:text-accent-foreground hover:bg-accent rounded">
            Clear All
          </button>
        </div>
      </div>

      {/* Orders Table */}
      <div className="flex-1 overflow-y-auto">
        <table className="w-full">
          <thead className="sticky top-0 bg-trading-panel border-b border-trading-border">
            <tr className="text-xs text-trading-text-secondary">
              <th className="py-2 px-2 text-left font-medium">Status</th>
              <th className="py-2 px-2 text-left font-medium">Symbol</th>
              <th className="py-2 px-2 text-center font-medium">Side</th>
              <th className="py-2 px-2 text-right font-medium">Quantity</th>
              <th className="py-2 px-2 text-right font-medium">Price</th>
              <th className="py-2 px-2 text-center font-medium">Type</th>
              <th className="py-2 px-2 text-right font-medium">Stop loss</th>
              <th className="py-2 px-2 text-right font-medium">Take profit</th>
              <th className="py-2 px-2 text-right font-medium">Fill Price</th>
              <th className="py-2 px-2 text-right font-medium">Bid</th>
              <th className="py-2 px-2 text-right font-medium">Ask</th>
              <th className="py-2 px-2 text-left font-medium">Order ID</th>
              <th className="py-2 px-2 text-left font-medium">Time and date</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <tr key={order.id} className="text-xs border-b border-trading-border/50 hover:bg-trading-border/30">
                <td className="py-2 px-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(order.status)}
                    <span className={`uppercase font-medium ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                </td>
                <td className="py-2 px-2 text-trading-text font-medium">{order.symbol}</td>
                <td className="py-2 px-2 text-center">
                  <span className={`uppercase font-medium ${
                    order.side === 'buy' ? 'text-bull-green' : 'text-bear-red'
                  }`}>
                    {order.side}
                  </span>
                </td>
                <td className="py-2 px-2 text-right text-trading-text">{order.quantity}</td>
                <td className="py-2 px-2 text-right text-trading-text">{order.price.toFixed(5)}</td>
                <td className="py-2 px-2 text-center text-trading-text">{order.type}</td>
                <td className="py-2 px-2 text-right text-trading-text">
                  {order.stopLoss ? order.stopLoss.toFixed(5) : '—'}
                </td>
                <td className="py-2 px-2 text-right text-trading-text">
                  {order.takeProfit ? order.takeProfit.toFixed(5) : '—'}
                </td>
                <td className="py-2 px-2 text-right text-trading-text">
                  {order.fillPrice ? order.fillPrice.toFixed(5) : '—'}
                </td>
                <td className="py-2 px-2 text-right text-trading-text">{order.bid.toFixed(5)}</td>
                <td className="py-2 px-2 text-right text-trading-text">{order.ask.toFixed(5)}</td>
                <td className="py-2 px-2 text-trading-text font-mono">{order.id}</td>
                <td className="py-2 px-2 text-trading-text">{order.timestamp}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};