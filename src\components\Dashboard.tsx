import React, { useState } from 'react';
import Header from './Header';
import PersonalStatsPanel from './PersonalStatsPanel';
import AccountInfoPanel from './AccountInfoPanel';
import PriceQuotesPanel from './PriceQuotesPanel';
import MainDataPanel from './MainDataPanel';
import TradingChart from './TradingChart';
import TradePanel from './TradePanel';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from './ui/resizable';

const Dashboard = () => {
  const [selectedChartSymbol, setSelectedChartSymbol] = useState('EURUSD');
  const [showChart, setShowChart] = useState(true);

  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col">
      <Header />
      
      {/* Main Layout Container */}
      <div className="flex-1 p-2 sm:p-4">
        <ResizablePanelGroup direction="vertical" className="h-full">
          {/* Top Section: Three-panel horizontal layout */}
          <ResizablePanel defaultSize={70} minSize={50}>
            <ResizablePanelGroup direction="horizontal" className="h-full">
              {/* Left Sidebar */}
              <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                <div className="h-full flex flex-col space-y-2">
                  {/* Price Quotes - Takes most space */}
                  <div className="flex-1 min-h-0">
                    <PriceQuotesPanel onSymbolSelect={setSelectedChartSymbol} />
                  </div>
                  
                  {/* Personal Stats - Collapsed by default */}
                  <div className="h-32">
                    <PersonalStatsPanel collapsed={true} />
                  </div>
                </div>
              </ResizablePanel>
              
              <ResizableHandle withHandle />
              
              {/* Center Panel - Main Chart Area */}
              <ResizablePanel defaultSize={55} minSize={40}>
                <div className="h-full bg-slate-800 rounded-lg border border-slate-700">
                  {showChart ? (
                    <TradingChart 
                      symbol={selectedChartSymbol}
                      className="h-full"
                      inline
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center text-gray-400">
                      <div className="text-center">
                        <div className="text-4xl mb-4">📈</div>
                        <p>Select a symbol to view chart</p>
                      </div>
                    </div>
                  )}
                </div>
              </ResizablePanel>
              
              <ResizableHandle withHandle />
              
              {/* Right Sidebar */}
              <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
                <div className="h-full flex flex-col space-y-2">
                  {/* Trade Panel - Takes most space */}
                  <div className="flex-1 min-h-0">
                    <TradePanel className="h-full" />
                  </div>
                  
                  {/* Account Info - Bottom section */}
                  <div className="h-40">
                    <AccountInfoPanel compact={true} />
                  </div>
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          </ResizablePanel>
          
          <ResizableHandle withHandle />
          
          {/* Bottom Panel - Data Tables (spans Left + Center only) */}
          <ResizablePanel defaultSize={30} minSize={20} maxSize={50}>
            <div className="h-full">
              <MainDataPanel />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
};

export default Dashboard;
